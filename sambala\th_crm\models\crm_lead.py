from odoo import fields, models, api, _
import json
from odoo.addons.queue_job.delay import group, chain
from odoo.exceptions import ValidationError, AccessError
from pytz import timezone, UTC
import xmlrpc.client
from datetime import datetime,timedelta
from markupsafe import Markup
from odoo.exceptions import UserError
import re
import logging
_logger = logging.getLogger(__name__)

PARTNER_FIELDS_TO_SYNC = [
    'lang',
    'mobile',
    'title',
    'function',
    'website',
]

# Subset of partner fields: sync all or none to avoid mixed addresses
PARTNER_ADDRESS_FIELDS_TO_SYNC = [
    'street',
    'street2',
    'city',
    'zip',
    'state_id',
    'country_id',
    'th_ward_id',
    'th_district_id',
    'th_ethnicity_id',
    'th_religion_id',
    'th_gender',
    'th_birthday',
    'th_place_of_birth_id',
    'th_street',
    'th_ward_permanent_id',
    'th_district_permanent_id',
    'th_state_id',
    'th_country_id',
]


class CrmLead(models.Model):
    _inherit = "crm.lead"

    # def _th_domain_place_of_birth(self):
    #     state_id = self.env['res.country.state'].search([]).filtered(lambda u: u.country_id == self.env.ref('base.vn'))
    #     return [('id', 'in', state_id.ids)]

    # def _compute_th_contact_domain(self):
    #     domain=[]
    #     crm_contact_ids = self.env['res.partner'].search(
    #                 [('th_module_ids', 'in', self.env.ref('th_setup_parameters.th_crm_module').ids)])
    #     if crm_contact_ids:
    #         domain.append(('id', 'in', crm_contact_ids.ids))
    #     return domain
    #
    # def _th_module_crm(self):
    #     crm_origin_ids = self.env.ref('th_setup_parameters.th_crm_module')
    #     return [('th_module_ids', 'in', crm_origin_ids.ids)]

    name_id_sequence = fields.Char(string="Mã cơ hội")
    email_from = fields.Char(readonly=True, tracking=True)
    phone = fields.Char(readonly=True, tracking=True)
    name = fields.Char(required=False, default="MỚI", copy=False)
    partner_id = fields.Many2one(string="Sinh viên", tracking=True)
    street = fields.Char(store=False, compute_sudo=True, tracking=True, readonly=True)
    street2 = fields.Char(store=False, compute_sudo=True)
    zip = fields.Char(store=False, readonly=True, compute_sudo=True)
    city = fields.Char(store=False, readonly=True, compute_sudo=True)
    state_id = fields.Many2one(readonly=True, store=False, compute_sudo=True, tracking=True)
    country_id = fields.Many2one(readonly=True, store=True, compute_sudo=True, tracking=True)
    title = fields.Many2one(store=False, tracking=True, readonly=True)
    function = fields.Char(store=False, tracking=True, readonly=True)
    contact_name = fields.Char(store=True)
    stage_id = fields.Many2one(string="Mối quan hệ", domain="[('th_type', '=', 'crm'),('th_auto', '=', False)]", group_expand="", ondelete='set null')
    th_is_stage_won = fields.Boolean(compute='_compute_th_is_stage_won', store=True)
    user_id = fields.Many2one(string="Người chăm sóc", domain="th_domain_user_id", default=False, tracking=True)
    team_id = fields.Many2one(string="Đội tư vấn",
                              domain="[('use_opportunities', '=', True),('th_origin_id', '=', th_origin_id)]", tracking=True)
    tag_ids = fields.Many2many(string="Nhóm cơ hội")

    th_description = fields.Text(string="Mô tả", tracking=True)
    th_last_check = fields.Datetime(string="Liên hệ lần cuối", tracking=True, copy=False, default=lambda self: fields.Datetime.now())

    th_customer_code = fields.Char(string="Mã khách hàng", related="partner_id.th_customer_code", readonly=True,
                                   tracking=True)
    th_gender = fields.Selection(string="Giới tính", selection=[('male', 'Nam'), ('female', 'Nữ'), ('other', 'Khác'), ],
                                 compute="_compute_partner_address_values", store=False,
                                 compute_sudo=True, tracking=True, readonly=True)
    th_birthday = fields.Date(string="Ngày sinh", compute="_compute_partner_address_values",
                              store=False, compute_sudo=True, tracking=True, readonly=True)
    th_place_of_birth_id = fields.Many2one(comodel_name="res.country.state", string="Nơi sinh",
                                           compute="_compute_partner_address_values",
                                           store=False, compute_sudo=True,
                                           tracking=True, readonly=True)
    th_ward_id = fields.Many2one(comodel_name='th.country.ward', string='Phường/ Xã',
                                 compute="_compute_partner_address_values", readonly=True,
                                 domain="[('th_district_id', '=?', th_district_id), ('th_district_id.th_state_id', '=?', state_id)]",
                                 store=False, compute_sudo=True, tracking=True)
    th_district_id = fields.Many2one(comodel_name='th.country.district', string='Quận/ Huyện',
                                     domain="[('th_state_id', '=?', state_id)]",
                                     compute="_compute_partner_address_values",
                                     readonly=True, store=False, compute_sudo=True, tracking=True)
    th_phone2 = fields.Char(string="Số điện thoại 2", compute='_compute_th_phone2',
                            readonly=True, store=True, tracking=True)
    th_ethnicity_id = fields.Many2one(comodel_name="th.ethnicity", string="Dân tộc",
                                      compute="_compute_partner_address_values",
                                      store=False, compute_sudo=True, tracking=True, readonly=True)
    th_religion_id = fields.Many2one(comodel_name="th.religion", string="Tôn giáo",
                                     compute="_compute_partner_address_values",
                                     store=False, compute_sudo=True, tracking=True, readonly=True)
    th_channel_id = fields.Many2one(comodel_name="th.info.channel", string="Kênh", tracking=True)
    th_source_name = fields.Char(string="Tên nguồn", tracking=True, copy=False)
    th_source_group_id = fields.Many2one(comodel_name="th.source.group", string="Nhóm nguồn", tracking=True, copy=False)
    th_admissions_station_id = fields.Many2one(comodel_name="th.admissions.station", string="Trạm tuyển sinh",
                                               tracking=True, copy=False)
    th_admissions_region_id = fields.Many2one(comodel_name="th.admissions.region", string="Vùng tuyển sinh",
                                              tracking=True, copy=False)
    th_registration_date = fields.Date(string="Ngày đăng ký", tracking=True, copy=False)
    th_level_up_date = fields.Date(string="Ngày lên Level", tracking=True, copy=False, default=False)
    th_status_group_id = fields.Many2one(comodel_name="th.status.category", string="Nhóm tình trạng", domain="[('th_type', '=', 'crm'), ('th_crm_level_ids', 'in', stage_id)]", tracking=True, copy=False)
    th_status_detail_id = fields.Many2one(comodel_name="th.status.detail",
                                          domain="[('th_status_category_id', '=?', th_status_group_id),('th_type', '=', 'crm'), ('th_crm_level_ids', 'in', stage_id)]",
                                          string="Trạng thái chi tiết", tracking=True, copy=False)
    th_major_ids = fields.Many2many(comodel_name="th.major", string="Ngành đăng ký(bỏ)", copy=False)
    th_major_id = fields.Many2one(comodel_name="th.major", string="Ngành đăng ký", copy=False, tracking=True)
    th_graduation_system_id = fields.Many2one(comodel_name="th.graduation.system", string="Hệ tốt nghiệp",
                                              tracking=True, copy=False)
    th_partner_referred_id = fields.Many2one(comodel_name="res.partner", string="Người giới thiệu", tracking=True, copy=False, index=True)
    th_affiliate_code = fields.Char(string="Mã Tiếp Thị Liên Kết", readonly=True,
                                    related='th_partner_referred_id.th_affiliate_code', tracking=True)
    # th_permanent_residence = fields.Char(string="Hộ khẩu thường trú")
    th_reuse_source = fields.Char(string="Tái sử dụng CSKH", tracking=True, copy=False)
    th_reuse = fields.Char(string="Tái sử dụng TVTS", copy=False, tracking=True)

    th_storage = fields.Boolean(string="Lưu trữ", default=False)
    th_uuid_mail_channel = fields.Char('uuid mail.channel')
    th_ownership_id = fields.Many2one(comodel_name="th.ownership.unit", string="Đơn vị sở hữu",
                                      default=lambda self: self.env.ref('th_setup_parameters.th_aum_ownership_unit').id,
                                      tracking=True)
    th_origin_id = fields.Many2one(comodel_name="th.origin", string="Trường", tracking=True)
    th_student_profile_id = fields.Many2one(comodel_name="th.student.profile", string="Hồ sơ")

    th_domain_stage_id = fields.Many2many(comodel_name="crm.stage", string="", compute="_compute_th_domain_stage_id")
    th_count_invoice = fields.Integer(string="Số đơn hàng", compute="_compute_th_count_invoice")
    th_invoice_status = fields.Boolean(string=" ", compute="_compute_th_count_invoice")
    th_tuition_handed = fields.Selection(selection=[('full', 'Đủ'), ('partial', 'Một phần')], string="Thanh toán học phí", copy=False)
    th_fees = fields.Boolean(string="Đã thanh toán lệ phí", copy=False)
    th_admission_decision = fields.Boolean(string="Đã trúng tuyển", copy=False)
    th_decision_id = fields.Many2one("th.admission.decision", string="Danh sách quyết định")
    th_street = fields.Char(string="Địa chỉ (Hộ khẩu)", compute="_compute_partner_address_values", tracking=True, readonly=True, compute_sudo=True)
    th_ward_permanent_id = fields.Many2one(comodel_name='th.country.ward', string='Phường/ Xã (Hộ khẩu)',
                                           domain="[('th_district_id', '=?', th_district_permanent_id), ('th_district_id.th_state_id', '=?', th_state_id)]",
                                           compute="_compute_partner_address_values",
                                           readonly=True, tracking=True, compute_sudo=True)
    th_district_permanent_id = fields.Many2one(comodel_name='th.country.district', string='Quận/ Huyện (Hộ khẩu)',
                                               compute="_compute_partner_address_values",
                                               domain="[('th_state_id', '=?', th_state_id)]", readonly=True, tracking=True, compute_sudo=True)
    th_state_id = fields.Many2one("res.country.state", string='Tỉnh/ TP (Hộ khẩu)', ondelete='restrict',
                                  compute="_compute_partner_address_values", readonly=True,
                                  domain="[('country_id', '=?', th_country_id)]", tracking=True, compute_sudo=True)
    th_country_id = fields.Many2one('res.country', string='Quốc gia (Hộ khẩu)', ondelete='restrict',
                                    compute="_compute_partner_address_values",
                                    readonly=True, tracking=True, compute_sudo=True)
    th_profile_batches_id = fields.Many2one(comodel_name="th.profile.batches", string="Đợt bàn giao", copy=False)
    th_student_code = fields.Char(string="Mã sinh viên", copy=False)
    th_result = fields.Selection(selection=[('keep', 'Giữ'), ('transfer', 'Chuyển')], string="Kết quả xử lý", copy=False)
    th_is_a_duplicate_opportunity = fields.Boolean(string="Là cơ hội trùng lặp", default=False, copy=False)
    th_key_duplicate = fields.Boolean(string="Key duplicate", copy=False)
    th_lead_duplicate_id = fields.Many2one(comodel_name="crm.lead", copy=False)
    th_admission_list_id = fields.Many2one(comodel_name="th.admission.list", copy=False, ondelete='cascade')
    th_enrollment_list = fields.Boolean(string="Đã trong danh sách khai giảng")
    th_enrollment_list_id = fields.Many2one(comodel_name="th.enrollment.list", string="Danh sách khai giảng")
    th_crm_code = fields.Char()
    th_check_admission = fields.Boolean("Chờ xét tuyển", copy=False)
    th_check_partner_referred = fields.Boolean(copy=False,string='Kiểm tra người giới thiệu')
    th_resolve_duplicate = fields.Boolean(copy=False, string='Xử lý trùng')
    th_domain_user_id = fields.Char(compute="_compute_th_domain_user")
    th_lead_aff_id = fields.Integer(string='Cơ hội bên aff', copy=False)
    th_hide_action_confirm_crm = fields.Boolean(default=False)
    th_data_getfly = fields.Text(string="Data Getfly", copy=False)
    th_crm_job = fields.Char(string="Nghề nghiệp", copy=False, tracking=True)
    th_stage_auto = fields.Boolean(string="Trạng thái tự động", related='stage_id.th_auto')
    th_withdraw_profile_id = fields.Integer(string="ID Mối quan hệ trước khi tạo HS")
    th_opportunity_list_partner_crm_ids = fields.One2many("crm.lead.opportunity.list.partner", "crm_lead_id",
                                                      copy=True, compute="_compute_th_opportunity_list_partner_crm_ids")
    th_date_of_delivery = fields.Date(related='th_student_profile_id.th_date_of_delivery', store=True)
    th_decision_date = fields.Date(related='th_student_profile_id.th_decision_date', store=True)
    th_profile_status = fields.Selection(related='th_student_profile_id.th_profile_status', store=True)
    th_is_refunded_tuition = fields.Boolean(string="Là cơ hội đã hoàn học phí", copy=False)
    th_is_refund_tuition = fields.Boolean(string="Là cơ hội cần hoàn học phí", copy=False)
    th_acceptance = fields.Char('Quyết định trúng tuyển')
    th_class = fields.Char('Khóa')
    th_class_detail = fields.Char('Lớp chuyên ngành')

    th_duplicate_processed_lead = fields.Boolean(string="Cơ hội đã xử lý trùng", copy=False, default=False)
    th_duplicate_description = fields.Text(string="Mô tả kiểm tra trùng", copy=False)
    th_duplicate_date = fields.Date(string="Ngày kiểm tra trùng", copy=False)
    th_duplicate_type = fields.Selection(selection=[('manual', 'Thủ công'), ('auto', 'Tự động'), ('need_handle', 'Cần xử lý'), ('no_results', 'Chưa có điều kiện')], string="Loại xử lý", copy=False, default='auto')

    th_code_getfly = fields.Char(string="Mã KH Getfly", copy=False)
    th_check_crm_phone = fields.Char(string="Điện thoại CRM")
    th_check_crm_email = fields.Char(string="Email CRM")
    state = fields.Selection(selection=[('keep', 'Đối tác tự chăm'), ('transfer', 'Tư vấn chăm')], string="Kiểu chăm", default='transfer')
    th_crm_lead_b2b_id = fields.Integer('ID cơ hội B2B', copy=False)
    th_import_phone = fields.Char(string="Check import Điện thoại")
    th_import_email = fields.Char(string="Check import Email")
    name_import = fields.Char(string="Tên import")
    th_utm_source = fields.Char('utm source', copy=False)
    th_utm_medium = fields.Char('utm medium', copy=False)
    th_utm_campaign = fields.Char('utm campaign', copy=False)
    th_utm_term = fields.Char('utm term', copy=False)
    th_utm_content = fields.Char('utm content', copy=False)
    th_dividing_ring_id = fields.Many2one(comodel_name='th.dividing.ring', string='Vòng chia')

    th_create_lead_date_getfly = fields.Date(string="Ngày tạo cơ hội Getfly")
    th_level_up_date_getfly = fields.Date(string="Ngày dự kiến lên L8")
    th_l5b_aof_date_getfly = fields.Date(string="Ngày lên L5B")
    th_reuse_ccs_getfly = fields.Char(string="Ngày tạo Lead")
    th_name_ccs_getfly = fields.Char(string="Tên CSKH")
    th_code_ccs_getfly = fields.Char(string="Mã nguồn CSKH")
    th_domain_major = fields.Char(compute="_compute_major_id_domain")
    th_customer_code_gf = fields.Char(string="Mã Khách Hàng Getfly")
    th_old_partner_id = fields.Many2one('res.partner')
    th_date_of_payment = fields.Date(string="Ngày thanh toán(bỏ)", store=True)
    th_settlement_date = fields.Date(string="Ngày thanh toán", related='order_ids.invoice_ids.invoice_date_due', store=True)
    th_training_system_id = fields.Many2one(comodel_name='th.training.system', string='Hệ đào tạo')
    th_uuid_form = fields.Char('Mã form', copy=False)
    th_form_name = fields.Char('Tên form nhúng', copy=False)
    th_required_fill = fields.Boolean(related="stage_id.th_required_fill", string="Bắt buộc điền")
    th_create_user_checked_id = fields.Many2one('res.users', string="Người tạo khi check trùng thắng")
    th_self_lead = fields.Boolean(string="Cơ hội tự chốt", tracking=True)
    readonly_domain = fields.Char(compute="_compute_readonly_domain")
    th_is_close_lead = fields.Boolean(string="Cơ hội bị đóng")
    th_first_create_day = fields.Date(string="Ngày tạo đầu tiên")
    th_dup_need_admin = fields.Boolean(string="Đánh dấu các cơ hội bị đúp nhưng không vào ma trận")
    th_customer_code_aum = fields.Char(string='Mã khách hàng')
    th_can_be_lead = fields.Boolean(string='Là trạng thái đánh dấu cơ hội có nhu cầu', related="th_status_detail_id.th_can_be_lead", store=True)
    th_is_apply_lead = fields.Boolean(string="Là cơ hội được admin duyệt tạo mới")
    th_date_lead_again = fields.Date(string="Ngày sau cùng vào hệ thống")
    th_confirm_self_lead = fields.Boolean(string="Đối soát cơ hội tự chốt", tracking=True)
    th_check_admin_crm = fields.Boolean(string="Check tài khoản admin CRM", compute="_compute_th_check_admin_crm")
    # th_check_tvts_crm = fields.Boolean(string="Check nhân viên TVTS", compute="_compute_th_check_tvts_crm")
    date = fields.Date(string="date")
    active = fields.Boolean('Active', default=True)
    th_user_complaints = fields.Many2one('res.users', string="Người khiếu nại")
    th_student_profile_archived = fields.Boolean(string="Hồ sơ đã được lưu trữ", default=False)
    th_ccs_status_detail_id = fields.Many2one(comodel_name="th.status.detail",
                                              string="Tình trạng gọi CSKH")
    th_ccs_customer_attitude_id = fields.Many2one(comodel_name="th.customer.attitude",
                                                  string="Phân loại thái độ khách hàng")
    th_ccs_reuse_origin_ids = fields.Many2many(comodel_name="th.origin",
                                               string="Trường từng TSD")
    th_ccs_source_code_ccs = fields.Char(string="Mã nguồn CSKH")
    th_ccs_source_code_reused = fields.Char(string="Mã nguồn CSKH từng TSD")
    th_check_unlink_record = fields.Boolean("Cơ hội cần xóa khi tái kho")
    th_check_waiting_lead = fields.Boolean("Cơ hội đang trong hàng chờ xử lý")
    activity_user_id = fields.Many2one('res.users', string='Người phụ trách(bỏ)')


    @api.depends('stage_id')
    def _compute_th_is_stage_won(self):
        for rec in self:
            rec.th_is_stage_won = rec.stage_id.is_won if rec.stage_id else False

    @api.model
    def _cron_delete_marked_leads(self):
        """Xóa các cơ hội đã được đánh dấu"""
        leads = self.search([
            ('th_check_unlink_record', '=', True),
            ('active', '=', False)
        ])
        _logger.info(f"Cron tìm thấy {len(leads)} cơ hội CRM để xóa.")

        if not leads:
            _logger.info("Không có cơ hội nào để xóa.")
            return

        if len(leads) > 100:
            _logger.info("Số lượng lớn, chia batch chạy queue")
            batch_size = 100
            for i in range(0, len(leads), batch_size):
                batch_ids = leads[i:i + batch_size].ids
                self.env['crm.lead'].sudo().with_delay(priority=20).bulk_delete_leads_job(batch_ids)
        else:
            _logger.info("Dưới 100 bản ghi -> Xóa ngay")
            leads._bulk_delete_leads()

    @api.model
    def bulk_delete_leads_job(self, lead_ids):
        """Hàm chạy trong queue, nhận danh sách ID"""
        leads = self.env['crm.lead'].sudo().browse(lead_ids)
        leads._bulk_delete_leads()

    def _bulk_delete_leads(self):
        for lead in self:
            try:
                lead.partner_id.th_check_module = True
                _logger.info(f"Xóa cơ hội ID={lead.id}")
                lead.sudo().with_context(force_delete=True).unlink()
            except Exception as e:
                _logger.exception(f"Xóa thất bại ID={lead.id}")

    # @api.depends('order_ids')
    # def _compute_th_status_payment_CRM(self):
    #     for rec in self:
    #         if rec.order_ids:
    #             if rec.order_ids.invoice_ids.mapped('payment_state') != [] \
    #                     and 'not_paid' not in rec.order_ids.invoice_ids.mapped('payment_state'):
    #                 rec.th_date_of_payment = fields.Date.today()
    #             else:
    #                 rec.th_date_of_payment = False
    #         else:
    #             rec.th_date_of_payment = False
    # def _compute_readonly_domain(self):
    #     for rec in self:
    #         rec.readonly_domain = False
    #         if rec.th_is_close_lead:
    #             rec.readonly_domain = json.dumps([])

    def _compute_th_check_admin_crm(self):
        for rec in self:
            rec = rec.with_context(crm_create=True)
            rec.th_check_admin_crm = False
            if self.env.user.has_group('th_crm.th_group_admin_crm'):
                rec.th_check_admin_crm = True

    # def _compute_th_check_tvts_crm(self):
    #     for rec in self:
    #         rec.th_check_tvts_crm = self.env.user.has_group('th_crm.th_group_crm_tvts')
    #         if self.env.user.has_group('th_crm.th_group_leader_crm') or self.env.user.has_group(
    #                 'th_crm.th_group_admin_crm'):
    #             rec.th_check_tvts_crm = False

    @api.depends('th_origin_id')
    def _compute_major_id_domain(self):
        for rec in self:
            domain = []
            # rec.with_user(self.env.ref('base.user_root')).action_refresh_level()
            if rec.th_origin_id:
                th_origin = self.env['th.university.major'].search([('th_origin_id', '=', rec.th_origin_id.id)]).mapped(
                    'th_major_id').ids
                domain.append(('id', 'in', th_origin))
            rec.th_domain_major = json.dumps(domain)

    def th_schedule_bring_leads_to_warehouse(self):
        today = fields.Date.today()
        stage = [self.env.ref('th_crm.th_stage_lead1').id,
                 self.env.ref('th_crm.th_stage_lead2').id,
                 self.env.ref('th_crm.th_stage_lead3').id,
                 self.env.ref('th_crm.th_stage_lead4').id,
                 self.env.ref('th_crm.th_stage_lead5').id,
                 self.env.ref('th_crm.th_stage_lead6').id,
                 self.env.ref('th_crm.th_stage_lead7').id]
        th_stage = self.env['crm.stage'].search([('id', 'in', stage),('th_auto_move_into_warehouse', '=', True)])
        th_status = self.env['th.status.detail'].search([('move_into_warehouse', '=', True)])

        # Tìm các leads đã hết hạn
        expired_leads = self.env['crm.lead'].search([
            ('th_last_check', '<=', today - timedelta(days=60)),
            ('stage_id', 'in', th_stage.ids),
            ('th_status_detail_id', 'in', th_status.ids)
        ])
        data_leads = [expired_leads[i:i + 300] for i in range(0, len(expired_leads), 300)]
        for data in data_leads:
            for res in data:
                exist_css = self.env['ccs.lead'].sudo().search([('th_lead_id', '=', res.id)], limit=1)
                res.th_storage = True
                if exist_css:
                    exist_css.th_block_css = False
                    exist_css.th_lead_id = False
                    msg = ("Cơ hội %s đã bị thu hồi về kho" % res.name)
                    exist_css.message_post(body=msg)
                # if res.th_crm_lead_b2b_id:
                #     res.with_delay()._sync_storage_lead()

    # def _sync_storage_lead(self, unlink = None):
    #     val = {}
    #     server_api = self.env['th.api.server'].search([('state', '=', 'deploy'), ('th_type', '=', 'aff')], limit=1, order='id desc')
    #     try:
    #         if not server_api:
    #             raise ValidationError('Không tìm thấy server!')
    #         result_apis = xmlrpc.client.ServerProxy('{}/xmlrpc/2/object'.format(server_api.th_url_api))
    #         db = server_api.th_db_api
    #         uid_api = server_api.th_uid_api
    #         password = server_api.th_password
    #         # data_crm = []
    #         for rec in self:
    #             if not unlink:
    #                 val = {
    #                     'th_storage': True
    #                 }
    #                 res_id = result_apis.execute_kw(db, uid_api, password, 'crm.lead', 'write', [rec.th_crm_lead_b2b_id, val], {'context': {'th_test_import': True}})
    #             else:
    #                 res_id = result_apis.execute_kw(db, uid_api, password, 'crm.lead', 'unlink', [[rec.th_crm_lead_b2b_id]])
    #     except Exception as e:
    #         print(e)
    #         self.env['th.log.api'].create({
    #             'state': 'error',
    #             'th_model': str(self._name),
    #             'th_description': str(e),
    #             'th_record_id': str(self.ids),
    #             'th_input_data': str(val),
    #             'th_function_call': str('_sync_storage_lead'),
    #         })
    #     return True

    @api.onchange('th_check_crm_phone')
    def onchange_th_check_crm_phone(self):
        self.ensure_one()
        check_module = self.env['ir.config_parameter'].sudo().get_param('th_check_partner_module')
        if self.th_check_crm_phone:
            domain = ['|', ('phone', '=', self.th_check_crm_phone), ('th_phone2', '=', self.th_check_crm_phone)]
            if check_module:
                domain.append(('th_module_ids', 'in', self.env.ref('th_setup_parameters.th_crm_module').ids))
            partner = self.env['res.partner'].search(domain, limit=1)
            if partner:
            #     duplicate_lead = self.env['crm.lead'].sudo().search(
            #         [('partner_id', '=', partner.id), ('partner_id', '!=', False),
            #          ('th_ownership_id', '=', self.th_ownership_id.id)], limit=1)
            #     if duplicate_lead:
                #     error_message = _(
                #         "Trùng cơ hội với tên đối tác '%s', Số điện thoại '%s', Số điện thoại 2 '%s', Email '%s', Người phụ trách '%s'!") % (
                #                         duplicate_lead.th_partner_id.name, duplicate_lead.th_partner_phone or '',
                #                         duplicate_lead.th_partner_phone2 or '',
                #                         duplicate_lead.th_partner_email or '', duplicate_lead.th_user_id.name or '')
                #     raise UserError(error_message)
                # else:
                    self.partner_id = partner

    @api.onchange('th_check_crm_email')
    def onchange_th_check_crm_email(self):
        self.ensure_one()
        check_module = self.env['ir.config_parameter'].sudo().get_param('th_check_partner_module')
        if self.th_check_crm_email:
            domain = [('email', '=', self.th_check_crm_email)]
            if check_module:
                domain.append(('th_module_ids', 'in', self.env.ref('th_setup_parameters.th_crm_module').ids))
            partner = self.env['res.partner'].search(domain, limit=1)
            if partner:
                self.partner_id = partner

    @api.onchange('th_origin_id')
    def _onchange_th_origin_id_major(self):
        self.th_major_id = False

    def action_close_lead(self):
        for rec in self:
            rec.th_is_close_lead = True
            msg = ("Cơ hội đã bị đóng, vui lòng không chỉnh sửa thêm!")
            rec.message_post(body=msg)

    def action_open_lead(self):
        for rec in self:
            rec.th_is_close_lead = False
            msg = ("Đã mở lại cơ hội")
            rec.message_post(body=msg)

    def action_lead_reuse(self):
        view_id = self.env.ref('th_crm.lead_reuse_view_form').id
        return {
            'name': 'Tái cơ hội',
            'view_mode': 'form',
            'res_model': 'crm.lead.reuse',
            'type': 'ir.actions.act_window',
            'views': [[view_id, 'form']],
            'context': {'action_assign': True, 'reuse_lead': True},
            'target': 'new',
        }

# tái kho lên cskh từ kho
    def th_create_ccs_lead(self, active_ids, value_reuse):
        index = 0
        user_ids = False
        th_ring_id = self.env['th.dividing.ring'].browse(value_reuse.get('ring_id', False))
        th_origin = value_reuse.get('id_origin', False)
        stage_ccs_id = self.env.ref('th_crm.th_stage_lead24').id
        th_source_code_css = value_reuse.get('th_source_code_css', False) if value_reuse.get('th_source_code_css', False) else False
        th_type_divide = value_reuse.get('th_type', False)
        opportunity_count = 0
        lst_reuse_id = []
        val_list = []
        # active_ids = self.env['crm.lead'].sudo().search([('id', 'in', active_ids)])
        active_ids.sudo().write({"th_check_unlink_record": True})
        for rec in active_ids:
            if th_type_divide == 'ring':
                user_ids = th_ring_id.action_assign_leads_dividing_ring()
            elif th_type_divide == 'user':
                user_ids = value_reuse.get('user_reuse_id', False)[index]
            query = """
                SELECT id,th_lead_id,th_opportunity_ready_count,th_origin_id FROM ccs_lead
                WHERE (th_phone = %(phone)s AND th_phone IS NOT NULL)
                   OR (th_phone2 = %(phone2)s AND th_phone2 IS NOT NULL)
                   AND th_origin_id = %(id_origin)s
                ORDER BY th_opportunity_ready_count DESC
                LIMIT 1;
            """

            params = {
                'phone': rec.phone if rec.phone else None,
                'phone2': rec.th_phone2 if rec.th_phone2 else None,
                'id_origin': value_reuse.get('id_origin', None)
            }

            self.env.cr.execute(query, params)
            opportunity_count_max = self.env.cr.fetchone()

            if opportunity_count_max:
                if opportunity_count_max[1] != None:
                    continue
                if opportunity_count_max:
                    opportunity_count = 0 if not opportunity_count_max[2] else opportunity_count_max[2]
                    opportunity_count = opportunity_count + 1
                else:
                    opportunity_count = 1
                if opportunity_count_max[3] == th_origin and opportunity_count_max[0] != None:
                    self.env['ccs.lead'].sudo().browse(opportunity_count_max[0]).update({
                        'th_opportunity_ready_count': opportunity_count,
                        'th_block_css': False,
                        'th_opportunity_ready': False,
                        'th_stage_id': stage_ccs_id,
                    })
                lst_reuse_id.append(rec.id)
            else:
                val = {
                    'th_reuse_source': value_reuse.get('name_reuse', False),
                    'th_lead_id': rec.id,
                    'name': rec.partner_id.name,
                    'th_partner_id': rec.partner_id.id,
                    'th_phone': rec.phone,
                    'th_phone2': rec.th_phone2,
                    'th_email': rec.email_from,
                    'th_admissions_region_id': rec.th_admissions_region_id.id,
                    'th_admissions_station_id': rec.th_admissions_station_id.id,
                    'th_major_ids': rec.th_major_ids.ids,
                    'th_major_id': rec.th_major_id.id,
                    'th_team_id': rec.team_id.id,
                    'th_dividing_ring_id': th_ring_id.id if th_ring_id else False,
                    'th_ownership_id': rec.th_ownership_id.id,
                    'th_reuse_origin_ids': [(6, 0, rec.th_ccs_reuse_origin_ids.ids)],
                    'th_source_name': rec.th_source_name,
                    'th_source_code_ccs': th_source_code_css,
                    'th_source_code_reused': rec.th_ccs_source_code_reused,
                    'th_user_id': user_ids,
                    'th_partner_referred_id': rec.th_partner_referred_id.id if rec.th_partner_referred_id else False,
                    'th_origin_id': th_origin if th_origin else rec.th_origin_id.id,
                    'th_channel_id': rec.th_channel_id.id,
                    'th_source_group_id': rec.th_source_group_id.id,
                    'th_gender': rec.th_gender,
                    'th_title_id': rec.title.id,
                    'th_function': rec.function,
                    'th_place_of_birth_id': rec.th_place_of_birth_id.id,
                    'th_ethnicity_id': rec.th_ethnicity_id.id,
                    'th_birthday': rec.th_birthday,
                    'th_religion_id': rec.th_religion_id.id,
                    'th_street': rec.street,
                    'th_ward_id': rec.th_ward_id.id,
                    'th_district_id': rec.th_district_id.id,
                    'th_state_id': rec.th_state_id.id,
                    'th_country_id': rec.country_id.id,
                    'th_street_permanent': rec.th_street,
                    'th_ward_permanent_id': rec.th_ward_permanent_id.id,
                    'th_district_permanent_id': rec.th_district_permanent_id.id,
                    'th_state_permanent_id': rec.th_state_id.id,
                    'th_country_permanent_id': rec.th_country_id.id,
                    'th_opportunity_ready_count': opportunity_count,
                    'th_opportunity_ready': False,
                }
                val_list.append(val)
                lst_reuse_id.append(rec.id)
            index = (index + 1) % len(value_reuse.get('user_reuse_id',False)) if th_type_divide == 'user' else 0
        new_css_lead = rec.env["ccs.lead"].sudo().with_context(crm_reuse=True).create(val_list)
        if lst_reuse_id:
            rec.env["crm.lead"].sudo().browse(lst_reuse_id).unlink()
            # query = "DELETE FROM crm_lead WHERE id IN %s"
            # self.env.cr.execute(query, (tuple(lst_reuse_id),))

    def action_withdraw_profile(self):
        if self.th_withdraw_profile_id:
            if not self.stage_id.is_won:
                self.stage_id = self.th_withdraw_profile_id
            self.th_student_profile_id.unlink()
            self.th_student_profile_id = False
            self.th_auto_next_level()

    def _compute_th_count_invoice(self):
        for rec in self:
            move = self.env['account.move'].search([('th_crm_lead_id', '=', rec.id)])
            rec.th_count_invoice = len(move) if move else 0
            rec.th_invoice_status = True if not move.filtered(lambda d: d.th_payment_status not in ('paid', 'over_payment')) else False

    def _compute_phone(self):
        res = super(CrmLead, self)._compute_phone()
        return res

    def _compute_email_from(self):
        res = super(CrmLead, self)._compute_email_from()
        for rec in self:
            if rec.partner_id and rec.partner_id.email != rec.email_from:
                rec.email_from = rec.partner_id.email
        return res

    @api.depends('stage_id')
    def _compute_th_domain_stage_id(self):
        data = self.env['crm.stage'].search([('th_auto', '=', False), ('th_type', '=', 'crm')]).ids
        for rec in self:
            if rec.stage_id and rec.stage_id.id not in data:
                data.append(rec.stage_id.id)
            rec.th_domain_stage_id = [(6, 0, data)]

    @api.depends('team_id', 'th_dividing_ring_id')
    def _compute_th_domain_user(self):
        for rec in self:
            domain = []
            if rec.team_id:
                team_member_ids = self.env['crm.team'].search([('id', '=', rec.team_id.id)]).member_ids.ids
                domain=[('id', 'in', team_member_ids)]
            if rec.th_dividing_ring_id:
                team_member_in_dividing_ring_ids = self.env['th.dividing.ring'].search([('id', '=', rec.th_dividing_ring_id.id)]).th_user_ids.ids
                domain=[('id', 'in', team_member_in_dividing_ring_ids)]
            rec.th_domain_user_id = json.dumps(domain)

    # @api.onchange('th_origin_id')
    # def _onchange_th_origin_id(self):
    #     if self.th_dividing_ring_id.th_origin_id.id != self.th_origin_id.id:
    #         self.th_dividing_ring_id = False

    @api.depends('user_id', 'type')
    def _compute_team_id(self):
        for lead in self:
            if not lead.user_id:
                continue
            user = lead.user_id
            if lead.team_id and user in (lead.team_id.member_ids | lead.team_id.user_id):
                continue
            lead.team_id = False

    # @api.onchange('th_dividing_ring_id')
    # def _onchange_th_dividing_ring_id(self):
    #     for rec in self:
    #         if rec.user_id and rec.user_id in rec.th_dividing_ring_id.mapped('th_user_ids'):
    #             pass
    #         else:
    #             rec.user_id = False
    #             rec.team_id = False

    @api.onchange('th_ward_id')
    def onchange_th_ward_id(self):
        if self.th_ward_id:
            self.th_district_id = self.th_ward_id.th_district_id.id
            self.state_id = self.th_district_id.th_state_id.id
            self.country_id = self.state_id.country_id.id

    @api.onchange('user_id')
    def onchange_user_id(self):
        if self.user_id and self.user_id.id in self.env['th.dividing.ring'].search([]).mapped('th_user_ids').ids:
            count = self.env['th.dividing.ring'].search_count(
                [('th_user_ids', '=', self.user_id.id), ('th_origin_id', '=', self.th_origin_id.id),
                 ('th_is_crm_lead_dividing', '=', True)])
            if count == 1:
                self.th_dividing_ring_id = self.env['th.dividing.ring'].search(
                    [('th_user_ids', '=', self.user_id.id), ('th_origin_id', '=', self.th_origin_id.id),
                     ('th_is_crm_lead_dividing', '=', True)])
                return {'domain': {'th_dividing_ring_id': [('id', 'in', self.env['th.dividing.ring'].search(
                    [('th_user_ids', '=', self.user_id.id), ('th_origin_id', '=', self.th_origin_id.id),
                     ('th_is_crm_lead_dividing', '=', True)]).ids)]}}
            elif count > 1:
                if not self.th_dividing_ring_id:
                    self.th_dividing_ring_id = self.env['th.dividing.ring'].search(
                        [('th_user_ids', '=', self.user_id.id), ('th_origin_id', '=', self.th_origin_id.id),
                         ('th_is_crm_lead_dividing', '=', True)], limit=1)
                return {'domain': {'th_dividing_ring_id': [('id', 'in', self.env['th.dividing.ring'].search(
                    [('th_user_ids', '=', self.user_id.id), ('th_origin_id', '=', self.th_origin_id.id),
                     ('th_is_crm_lead_dividing', '=', True)]).ids)]}}
        else:
            self.th_dividing_ring_id = False
            self.team_id = False
            return {'domain': {'th_dividing_ring_id': [('id', 'in', self.env['th.dividing.ring'].search(
                [('th_is_crm_lead_dividing', '=', True), ('th_origin_id', '=?', self.th_origin_id.id)]).ids)]}}

    # @api.onchange('th_district_id')
    # def onchange_th_district_id(self):
    #     if self.th_district_id:
    #         self.state_id = self.th_district_id.th_state_id.id
    #         self.country_id = self.state_id.country_id.id
    #     if self.th_district_id != self.th_ward_id.th_district_id:
    #         self.th_ward_id = False
    #
    # @api.onchange('country_id')
    # def onchange_th_country_id(self):
    #     if self.country_id != self.state_id.country_id:
    #         self.state_id = False
    #
    # @api.onchange('state_id')
    # def onchange_th_state_id(self):
    #     if self.state_id:
    #         self.country_id = self.state_id.country_id.id
    #     if self.state_id != self.th_district_id.th_state_id:
    #         self.th_district_id = False
    #
    # @api.onchange('th_ward_permanent_id')
    # def onchange_th_ward_permanent_id(self):
    #     if self.th_ward_permanent_id:
    #         self.th_district_permanent_id = self.th_ward_permanent_id.th_district_id.id
    #         self.th_state_id = self.th_district_permanent_id.th_state_id.id
    #         self.th_country_id = self.th_state_id.country_id.id
    #
    # @api.onchange('th_district_permanent_id')
    # def onchange_th_district_permanent_id(self):
    #     if self.th_district_permanent_id:
    #         self.th_state_id = self.th_district_permanent_id.th_state_id.id
    #         self.th_country_id = self.th_state_id.country_id.id
    #     if self.th_district_permanent_id != self.th_ward_permanent_id.th_district_id:
    #         self.th_ward_permanent_id = False
    #
    # @api.onchange('th_country_id')
    # def onchange_th_country_permanent_id(self):
    #     if self.th_country_id != self.th_state_id.country_id:
    #         self.th_state_id = False
    #
    # @api.onchange('th_state_id')
    # def onchange_th_state_permanent_id(self):
    #     if self.th_state_id:
    #         self.th_country_id = self.th_state_id.country_id.id
    #     if self.th_state_id != self.th_district_permanent_id.th_state_id:
    #         self.th_district_permanent_id = False

    def _prepare_address_values_from_partner(self, partner):
        # Sync all address fields from partner, or none, to avoid mixing them.
        if partner and any(partner[f] for f in PARTNER_ADDRESS_FIELDS_TO_SYNC):
            values = {f: partner[f] for f in PARTNER_ADDRESS_FIELDS_TO_SYNC}
        else:
            values = {f: self[f] for f in PARTNER_ADDRESS_FIELDS_TO_SYNC}
        return values

    @api.depends('partner_id')
    def _compute_partner_address_values(self):
        """ Sync all or none of address fields """
        for lead in self:
            lead.update(lead._prepare_address_values_from_partner(lead.partner_id))

    def _get_partner_phone2_update(self):
        """Calculate if we should write the phone on the related partner. When
        the phone of the lead / partner is an empty string, we force it to False
        to not propagate a False on an empty string.

        Done in a separate method so it can be used in both ribbon and inverse
        and compute of phone update methods.
        """
        self.ensure_one()
        if self.partner_id and self.th_phone2 != self.partner_id.th_phone2:
            lead_phone_formatted = self.phone_get_sanitized_number(number_fname='th_phone2') or self.th_phone2 or False
            partner_phone_formatted = self.partner_id.phone_get_sanitized_number(
                number_fname='th_phone2') or self.partner_id.th_phone2 or False
            return lead_phone_formatted != partner_phone_formatted
        return False

    @api.onchange('th_status_group_id')
    def onchange_th_status_group_id(self):
        if self.th_status_detail_id and self.th_status_detail_id.th_status_category_id != self.th_status_group_id:
            self.th_status_detail_id = False

    @api.depends('partner_id.th_phone2')
    def _compute_th_phone2(self):
        for lead in self:
            if lead.partner_id.th_phone2 and lead._get_partner_phone2_update():
                lead.th_phone2 = lead.partner_id.th_phone2
            elif lead.partner_id and lead.partner_id.th_phone2 != lead.th_phone2:
                lead.th_phone2 = False

    def write(self, values):
        th_last_check = False
        need_user_id = False
        is_crm_create = self.env.user.has_group('th_crm.th_group_crm_can_create') and not self.env.user.has_group('th_crm.th_group_admin_crm')
        if is_crm_create:
            values.pop('th_last_check', None)
        res = super(CrmLead, self).write(values)

        if values.get('date_action_last', False) and len(values.keys()) == 1:
            return
        if (self.env.user.has_group('th_crm.th_group_crm_can_create')
                and not self._context.get('import_file', False)
                and not self.env.user.has_group('th_crm.th_group_admin_crm')
                and not self._context.get('crm_create')
                and not set(values).issubset({'th_last_check'})
                and not set(values).issubset({'message_main_attachment_id'})):
            raise ValidationError("Bạn không được phép chỉnh sửa cơ hội này!")
        if 'partner_id' in values:
            for rec in self:
                rec.th_old_partner_id = rec.partner_id
        if values.get('th_student_profile_id', False):
            for rec in self:
                self.env['th.student.profile'].browse(values.get('th_student_profile_id', False)).th_lead_id = rec.id
        if self._context.get('manual') and self._context.get('uid') not in [1,2]:
            if values.get('date_action_last', False) and len(values.keys())==1:
                pass
            else:
                for rec in self:
                    values['th_duplicate_type'] = 'manual'
        if (not self._context.get('th_test_import', False) and not self._context.get('import_file', False)
            and ('description' in values and values.get('description') or 'th_status_detail_id' in values and values.get('th_status_detail_id'))):
            for rec in self:
                values['th_last_check'] = fields.Date.today()
        # if values.get('team_id') and self._context.get('reuse'):
        #     for rec in self:
        #         values['user_id'] = self.env['crm.team'].browse(values.get('team_id')).action_assign_leads(rec)[
        #             values.get('team_id')]
        for rec in self:
            need_user_id = True if (values.get('th_is_a_duplicate_opportunity') == False and rec.th_dividing_ring_id and not rec.user_id) else False
            # if rec.th_is_close_lead and not 'stage_id' in values:
            #     raise ValidationError("Cơ hội đã đóng! Vui lòng không chỉnh sửa")
        res = super(CrmLead, self.with_context(mail_create_nosubscribe=True, mail_post_autofollow=False)).write(values)
        if 'partner_id' in values:
            values['th_check_module'] = True
        if (values.get('th_dividing_ring_id') and not self.user_id) or (values.get('state') == 'transfer' and values.get('th_dividing_ring_id')):
            self.with_context(th_test_import=True).user_id = self.th_dividing_ring_id.action_assign_leads_dividing_ring()
        for rec in self:
            if need_user_id and rec.th_dividing_ring_id and not rec.user_id and not rec.th_is_a_duplicate_opportunity:
                rec.user_id = rec.th_dividing_ring_id.action_assign_leads_dividing_ring()
            if not self._context.get('th_test_import', False):
                if values.get('th_tuition_handed') or values.get('th_fees') or values.get('th_admission_decision') or values.get('th_student_profile_id') or values.get('th_enrollment_list'):
                    if rec.th_auto_next_level().th_crm_level:
                        rec.with_context(th_test_import=True).stage_id = rec.th_auto_next_level().th_crm_level.id
                if values.get('partner_id') or values.get('th_origin_id'):
                    exist_lead = self.env['crm.lead'].sudo().search(
                        [('id', '!=', rec.id), ('th_origin_id', '=', rec.th_origin_id.id),
                         ('partner_id', '=', rec.partner_id.id), ('th_is_a_duplicate_opportunity', '=', False)])
                    if len(exist_lead)>1:
                        exist_lead = self.env['crm.lead'].sudo().search(
                            [('id', '!=', rec.id), ('th_origin_id', '=', rec.th_origin_id.id),
                             ('partner_id', '=', rec.partner_id.id), ('th_is_a_duplicate_opportunity', '=', False), ('th_is_close_lead', '=', False)])
                        if not exist_lead:
                            exist_lead = self.env['crm.lead'].sudo().search(
                                [('id', '!=', rec.id), ('th_origin_id', '=', rec.th_origin_id.id),
                                 ('partner_id', '=', rec.partner_id.id), ('th_is_a_duplicate_opportunity', '=', False)],limit=1)
                    if exist_lead:
                        msg = (" Cơ hội này đã trùng với cơ hội %s Số điện thoại: %s" % (exist_lead.name, exist_lead.partner_id.phone))
                        rec.message_post(body=msg)
                        # rec.with_context(th_test_import=True).th_is_a_duplicate_opportunity = True
                        rec.th_check_condition_lead(exist_lead, rec)
                        self.env['bus.bus'].sudo()._sendone(
                            self.env.user.partner_id,
                            "simple_notification",
                            {
                                "title": "Warning",
                                "message": msg,
                                'sticky': True,
                                "warning": True
                            }
                        )
                if values.get('th_partner_referred_id', ''):
                    rec.th_check_partner_referred = True
                # keys = [
                #     'th_ownership_id', 'th_origin_id', 'th_source_name', 'th_description', 'th_status_detail_id',
                #     'th_channel_id', 'th_source_group_id', 'stage_id', 'th_admissions_region_id',
                #     'th_admissions_station_id', 'state', 'th_major_id', 'th_status_group_id', 'phone',
                #     'email_from', 'th_phone2', 'user_id', 'th_graduation_system_id']
                # if rec.th_last_check.date() != th_last_check and th_last_check:
                #     keys.append('th_last_check')
                # if list(filter(lambda x: x in keys, list(values.keys()))) and rec.th_ownership_id.th_is_sync and not self._context.get('th_test_import', False):
                #     return True
                #     rec.setup_data_crm(rec)
        return res

    def action_open_profile(self):
        self.ensure_one()
        return {
            'name': 'Hồ sơ',
            'view_mode': 'form',
            'res_model': 'th.student.profile',
            'type': 'ir.actions.act_window',
            'target': 'new',
            'context': {'default_th_lead_id': self.id, 'no_create': False},
            'res_id': self.th_student_profile_id.id,
            'domain': [('id', '=', self.th_student_profile_id.id)],
        }

    def change_admission(self):
        for rec in self:
            if rec.th_student_profile_id:
                if not rec.th_student_profile_id.th_date_of_delivery:
                    rec.th_student_profile_id.th_date_of_delivery = rec.th_admission_list_id.create_date
                rec.th_student_profile_id.th_handover_status = 'handed_over'
            rec.th_check_admission = True
            if rec.th_auto_next_level():
                rec.stage_id = rec.th_auto_next_level().th_crm_level.id
        return True

    def _get_lead_sale_order_domain(self):
        return [('state', 'not in', ('sent', 'cancel'))]

    def action_create_profile(self):
        self.ensure_one()
        decision_create_date = False
        default_handover_status = 'not_handed'
        self.th_withdraw_profile_id = self.stage_id
        if self.th_check_admission:
            default_handover_status = 'handed_over'
            decision_create_date = self.th_admission_list_id.create_date
        profile_exist = self.env['th.student.profile'].sudo().search([('th_lead_id', '=', self.id)], limit=1)
        if profile_exist:
            self.th_student_profile_id = profile_exist
            if self.th_auto_next_level():
                self.stage_id = self.th_auto_next_level().th_crm_level.id
            return True
        return {
            'name': 'Tạo hồ sơ',
            'view_mode': 'form',
            'res_model': 'th.student.profile',
            'type': 'ir.actions.act_window',
            'target': 'new',
            'context': {'default_th_lead_id': self.id,
                        'default_th_origin_id': self.th_origin_id.id,
                        'default_th_partner_id': self.partner_id.id,
                        'default_th_first_and_last_name': self.partner_id.name,
                        'default_th_profile_status': 'missing',
                        'default_th_date_of_delivery': decision_create_date,
                        'default_th_handover_status': default_handover_status,
                        'default_th_citizen_identification': self.partner_id.th_citizen_identification,
                        'default_th_date_iden': self.partner_id.th_date_identification,
                        'default_th_street': self.partner_id.street,
                        'default_th_ward_id': self.partner_id.th_ward_id.id,
                        'default_th_district_id': self.partner_id.th_district_id.id,
                        'default_th_state_id': self.partner_id.state_id.id,
                        'default_th_country_id': self.partner_id.country_id.id,
                        'default_th_street_permanent': self.partner_id.th_street,
                        'default_th_ward_permanent_id': self.partner_id.th_ward_permanent_id.id,
                        'default_th_district_permanent_id': self.partner_id.th_district_permanent_id.id,
                        'default_th_state_permanent_id': self.partner_id.th_state_id.id,
                        'default_th_country_permanent_id': self.partner_id.th_country_id.id,
                        'default_th_ethnicity_id': self.partner_id.th_ethnicity_id.id,
                        'default_th_religion_id': self.partner_id.th_religion_id.id,
                        'default_th_place_of_birth_id': self.partner_id.th_place_of_birth_id.id,
                        'default_th_gender': self.partner_id.th_gender,
                        'default_th_birthday': self.partner_id.th_birthday,
                        },
        }

    # Thu hồi về kho
    def th_archive_lead(self, data):
        val = {
            'th_storage': True,
            'th_student_profile_archived': True,
        }
        if data.mapped('th_student_profile_id'):
            data.mapped('th_student_profile_id').write({'active': True})
        data.write(val)
        exist_css = self.env['ccs.lead'].sudo().search([('th_lead_id', 'in', data.ids)])
        if exist_css:
            exist_css.write({
                'th_block_css': False,
                'th_lead_id': False,
            })
            for rec in exist_css:
                msg = ("Cơ hội đã bị thu hồi về kho")
                rec.message_post(body=msg)

    def th_finish_divide_lead(self):
        self.sudo().write({'th_check_waiting_lead': False})

    def th_action_archive(self):
        self.write({'th_check_waiting_lead': True})
        lead_crm = []
        for i in range(0, len(self.ids), 100):
            lead_crm.append(self[i:i + 100])
        for data in lead_crm:
            group_divide_action = (self.sudo().delayable().with_delay().th_archive_lead(data))
            group_done = (self.delayable().th_finish_divide_lead())
            chain(group_divide_action, group_done).delay()
            # self.sudo().with_delay().th_archive_lead(data)
        return {
            'type': 'ir.actions.client',
            'tag': 'reload',
        }

        # for rec in self:
        #     rec.th_storage = True
        #     rec.th_student_profile_id.active = False if rec.th_student_profile_id else False
        #     rec.th_student_profile_archived = True
        #     exist_css = self.env['ccs.lead'].sudo().search([('th_lead_id', '=', rec.id)], limit=1)
        #     if exist_css:
        #         # exist_css.th_opportunity_ready = False
        #         exist_css.th_block_css = False
        #         exist_css.th_lead_id = False
        #         msg = ("Cơ hội %s đã bị thu hồi về kho" % rec.name)
        #         exist_css.message_post(body=msg)
        #     # if rec.th_crm_lead_b2b_id:
        #     #     rec._sync_storage_lead()
        # return {
        #     'type': 'ir.actions.client',
        #     'tag': 'reload',
        # }

    def action_create_reuse(self):
        context = self.env.context.copy()
        context.update({'default_th_create_reuse': True,
                        'default_th_invisible_university': True})
        self.env['crm.lead.reuse'].browse(self._context.get('active_id')).write({'th_create_reuse': True,
                                                                                 'th_invisible_university': True})
        return {
            'name': 'Điều kiện chia cơ hội',
            'view_mode': 'form',
            'res_model': 'crm.lead.reuse',
            'type': 'ir.actions.act_window',
            'target': 'new',
            'res_id': self._context.get('active_id'),
            'context': context,
        }

    @api.model_create_multi
    def create(self, vals_list):
        for vals in vals_list:
            if self._context.get('import_file') and (vals.get('th_import_phone', False) or vals.get('th_import_email', False)):
                partner_phone = False
                partner_email = False
                module = self.env.ref('th_setup_parameters.th_crm_module')
                check_module = self.env['ir.config_parameter'].sudo().get_param('th_check_partner_module')
                if vals.get('th_import_phone', False):
                    domain = ['|', ('phone', '=', vals['th_import_phone']), ('th_phone2', '=', vals['th_import_phone'])]
                    if check_module:
                        domain.append(('th_module_ids', 'in', module.ids))
                    partner_phone = self.env['res.partner'].sudo().search(domain, limit=1)
                    if partner_phone:
                        vals.update({
                            'partner_id': partner_phone.id
                        })
                if not partner_phone and vals.get('th_import_email', False):
                    domain = [('email', '=', vals['th_import_email'])]
                    if check_module:
                        domain.append(('th_module_ids', 'in', module.ids))
                    partner_email = self.env['res.partner'].sudo().search(domain, limit=1)
                    if partner_email:
                        vals.update({
                            'partner_id': partner_email.id,
                        })
                if not partner_phone and not partner_email:
                    phone = vals.get('th_import_phone', False)

                    if phone:
                        phone = re.sub(r'[^\d]', '', phone)
                        # Kiểm tra nếu số bắt đầu bằng +84 hoặc 84 và có tổng 11 chữ số
                        if phone.startswith('84') and len(phone) == 11:
                            phone = '0' + phone[2:]

                        # Thêm số 0 vào đầu nếu thiếu
                        if not phone.startswith('0'):
                            phone = '0' + phone
                    partner = self.sudo().env['res.partner'].sudo().create(
                        {'name': vals.get('name_import', False),
                         'phone': phone,
                         'email': vals.get('th_import_email', False),
                         'th_check_module': True,
                         })

                    vals.update({
                        'partner_id': partner.id,
                    })
            if not vals.get('th_customer_code_aum'):
                vals['th_customer_code_aum'] = self.env['res.partner'].search([('id', '=', vals.get('partner_id'))]).th_customer_code
            if not vals.get('th_origin_id', False):
                vals['th_origin_id'] = self.env.ref('th_setup_parameters.th_aum_university_origin').id
            if not vals.get('th_last_check', False):
                vals['th_last_check'] = fields.Datetime.now()
            if self._context.get('web_form', False):
                vals['th_channel_id'] = self.env.ref('th_setup_parameters.th_aum_info_channel_web').id
            if self._context.get('livechat', False):
                vals['th_channel_id'] = self.env.ref('th_setup_parameters.th_aum_info_channel_chat_web').id
            vals['th_crm_code'] = self.env['ir.sequence'].next_by_code('crm.code')
            if vals.get('th_partner_referred_id'):
                vals['th_check_partner_referred'] = True
            # if vals.get('th_dividing_ring_id', False) and not vals.get('user_id', False):
            #     person_in_charge = self.env['th.dividing.ring'].browse(
            #         vals.get('th_dividing_ring_id', [])).action_assign_leads_dividing_ring()
            #     vals['user_id'] = person_in_charge
            if not vals.get('th_dividing_ring_id', False) and not vals.get('user_id', False):
                vals['user_id'] = self.env.user.id
        res = super(CrmLead, self.with_context(mail_create_nosubscribe=True, mail_post_autofollow=False)).create(vals_list)
        res = res.sudo().with_context(crm_create=True)
        for rec in res:
            rec.th_registration_date = UTC.localize(rec.create_date).astimezone(timezone(self.env.user.tz or 'Asia/Ho_Chi_Minh')).replace(tzinfo=None)
            if rec.partner_id:
                rec.partner_id.th_check_module = True
            rec.write({'stage_id': self.env.ref('th_crm.th_stage_lead1').id})
        name_id = self.env['ir.sequence'].next_by_code('crm.lead')
        if res.name == 'MỚI' and res.partner_id:
            res.with_context(th_test_import=True).name_id_sequence = name_id
            res.with_context(th_test_import=True).name = "[" + name_id + "]" + "-" + res.partner_id.name

        try:
            for rec in res:
                if self._context.get('th_test_import', False):
                    return res
                if rec.th_origin_id:
                    exist_lead_ccs = self.env['ccs.lead'].sudo().search(
                        [('th_origin_id', '=', rec.th_origin_id.id), ('th_lead_id', '!=', rec.id),('th_lead_id', '=', False),
                         '|', ('th_opportunity_ready', '=', False), ('th_block_css', '=', False),
                         '|', '&', ('th_phone', '=', rec.phone), ('th_phone', '!=', False),
                            '&', ('th_phone2', '=', rec.th_phone2), ('th_phone2', '!=', False)])
                    if self._context.get('id_ccs', False) and exist_lead_ccs:
                        create_ccs = self.sudo().env['ccs.lead'].sudo().browse(self._context.get('id_ccs', False))
                        exist_lead_ccs = exist_lead_ccs - create_ccs
                    if exist_lead_ccs:
                        for record in exist_lead_ccs:
                            record.th_block_css = True
                            msg = ("Cơ hội này đã bị block vì đã có cơ hội mới %s" % rec.name)
                            record.message_post(body=msg)
                    exist_lead = self.env['crm.lead'].sudo().search(
                        [('id', '!=', rec.id), ('th_origin_id', '=', rec.th_origin_id.id),
                         ('partner_id', '=', rec.partner_id.id), ('th_is_a_duplicate_opportunity', '=', False)])
                    if len(exist_lead)>1:
                        exist_lead = self.env['crm.lead'].sudo().search(
                            [('id', '!=', rec.id), ('th_origin_id', '=', rec.th_origin_id.id),
                             ('partner_id', '=', rec.partner_id.id), ('th_is_a_duplicate_opportunity', '=', False),
                             ('stage_id.is_won', '=', False)],limit=1)
                        if not exist_lead:
                            exist_lead = self.env['crm.lead'].sudo().search(
                                [('id', '!=', rec.id), ('th_origin_id', '=', rec.th_origin_id.id),
                                 ('partner_id', '=', rec.partner_id.id), ('th_is_a_duplicate_opportunity', '=', False),
                                 ('th_is_close_lead', '=', False)], limit=1)
                        if not exist_lead:
                            exist_lead = self.env['crm.lead'].sudo().search(
                                [('id', '!=', rec.id), ('th_origin_id', '=', rec.th_origin_id.id),
                                 ('partner_id', '=', rec.partner_id.id), ('th_is_a_duplicate_opportunity', '=', False)],limit=1)
                    if exist_lead:
                        msg = (" Cơ hội này đã trùng với cơ hội %s, \n\n Số điện thoại: %s" % (exist_lead.name, exist_lead.partner_id.phone))
                        rec.message_post(body=msg)
                        rec.th_check_condition_lead(exist_lead, rec)
                        self.env['bus.bus'].sudo()._sendone(
                            (self._cr.dbname, 'res.partner', self.env.user.partner_id.id),
                            'simple_notification',
                            {

                                'title': 'Kiểm tra kết nối thành công!',
                                'message': msg,
                                'sticky': True,
                                'warning': False
                            })
                    if not exist_lead and rec.th_dividing_ring_id and not rec.user_id:
                        rec.user_id = rec.th_dividing_ring_id.action_assign_leads_dividing_ring()
        except Exception as e:
            print(e)

        return res

    def create_lead_aff(self, vals):
        th_origin = False
        th_source_group_id = False
        th_partner_referred_id = False
        th_channel_id = self.env.ref('th_setup_parameters.th_channel_unknown').id
        th_ownership = self.env['th.ownership.unit'].search([('th_code', '=', self._context.get('th_ownership_code'))], limit=1)
        check_module = self.env['ir.config_parameter'].sudo().get_param('th_check_partner_module')
        th_major_id = self.env['th.university.major'].search([('th_major_code_university', '=', vals.get('th_major_code_university'))], limit=1).th_major_id if vals.get('th_major_code_university', False) else False
        if vals.get('th_warehouse_code', False):
            th_origin = self.env['th.origin'].search([('th_code', '=', vals.get('th_warehouse_code'))], limit=1)
        if vals.get('th_utm_source', False):
            th_source_group_id = self.env['th.source.group'].search([('name', '=', vals.get('th_utm_source', False))], limit=1).id
            if not th_source_group_id:
                th_source_group_id = self.env['th.source.group'].create({
                    'name': vals.get('th_utm_source', False),
                }).id

        if vals.get('th_affiliate_code', False):
            th_partner_referred_id = self.env['res.partner'].search([('th_affiliate_code', '=', vals.get('th_affiliate_code'))], limit=1)
        domain = []
        if vals.get('th_phone', False):
            domain = ['|', ('phone', '=', vals.get('th_phone')), ('th_phone2', '=', vals.get('th_phone'))]
        if vals.get('th_email', False):
            domain = ['|',] + domain + [('email', '=', vals.get('th_email'))] if domain else [('email', '=', vals.get('th_email'))]
        if check_module:
            domain.append(('th_module_ids', 'in', self.env.ref('th_setup_parameters.th_crm_module').ids))
        partner_aff = False
        if domain:
            partner_aff = self.env['res.partner'].search(domain, limit=1)
            if partner_aff.phone and not partner_aff.th_phone2 and vals.get('th_phone', False) and partner_aff.phone != vals.get('th_phone', False):
                partner_aff.write({
                    'th_phone2': vals.get('th_phone'),
                })
            elif not partner_aff.phone and vals.get('th_phone', False):
                partner_aff.write({
                    'phone': vals.get('th_phone'),
                })
            elif partner_aff.th_phone2 and partner_aff.phone and vals.get('th_phone', False) and vals.get('th_phone', False) not in [partner_aff.th_phone2, partner_aff.phone]:
                partner_aff.write({
                    'comment': Markup(
                        partner_aff.comment + 'Số điện thoại mới: ' + vals.get('th_phone', False)) if partner_aff.comment else Markup(
                        'Số điện thoại mới: ' + vals.get('th_phone', False))
                })

        if not partner_aff:
            partner_aff = self.env['res.partner'].create({
                'name': vals['th_customer'] if vals.get('th_customer') else vals.get('th_phone'),
                'phone': vals.get('th_phone', False),
                'email': vals.get('th_email', False),
                'th_check_module': True,
            })

        new_opportunity = {
            'partner_id': partner_aff.id,
            'th_origin_id': th_origin.id if th_origin else self.env.ref('th_setup_parameters.th_aum_university_origin').id,
            'th_major_id': th_major_id.id if th_major_id else False,
            'th_last_check': fields.Datetime.now(),
            'th_partner_referred_id': th_partner_referred_id.id if th_partner_referred_id else False,
            'th_lead_aff_id': vals.get('id', False),
            'th_ownership_id': th_ownership.id if th_ownership else False,
            'th_description': vals.get('th_description', False),
            'th_source_name': vals.get('th_source_name', False),
            'th_utm_source': vals.get('th_utm_source', False),
            'th_utm_medium': vals.get('th_utm_medium', False),
            'th_utm_campaign': vals.get('th_utm_campaign', False),
            'th_utm_term': vals.get('th_utm_term', False),
            'th_utm_content': vals.get('th_utm_content', False),
            'th_source_group_id': th_source_group_id,
            'th_channel_id': th_channel_id,
            'th_form_name': vals.get('th_form_name', False),
            'th_uuid_form': vals.get('th_uuid_form', False),
        }
        if self._context.get('th_form_id') and self._context.get('aff_crm_lead_form'):
            th_formio_pro_id = self.env['th.formio.builder.field.aff.default'].search(
                [('th_uuid', '=', self._context.get('th_form_id'))])
            if th_formio_pro_id:
                # th_formio_pro_id.th_ownership_unit_id = th_ownership.id if th_ownership else False
                new_opportunity.update({
                    # 'th_origin_id': th_formio_pro_id.th_origin_id.id if  th_formio_pro_id.th_origin_id else False,
                    # 'th_ownership_id': th_formio_pro_id.th_ownership_unit_id.id if th_formio_pro_id.th_ownership_unit_id else False,
                    'th_dividing_ring_id':  th_formio_pro_id.th_dividing_ring_id.id,
                    'th_status_group_id': th_formio_pro_id.th_status_group_id.id if th_formio_pro_id.th_status_group_id else False,
                    'th_status_detail_id': th_formio_pro_id.th_status_detail_id.id if th_formio_pro_id.th_status_detail_id else False,
                })
        try:
            crm_lead = self.sudo().create(new_opportunity)
            if vals.get('id', False) and crm_lead:
                exist_lead = False

                for rec in crm_lead:
                    exist_lead = self.env['crm.lead'].sudo().search(
                        [('id', '!=', rec.id), ('th_origin_id', '=', rec.th_origin_id.id),
                         ('partner_id', '=', rec.partner_id.id), ('th_is_a_duplicate_opportunity', '=', False)],
                        limit=1)
                    val_return = {
                        'crm_id': crm_lead.id,
                        'duplicate': False if not exist_lead else True,
                    }

                    th_last_check = (fields.Date.today() - exist_lead.th_last_check.date()).days
                    check_condition = self.env['th.check.condition'].search(
                        [('th_date_from', '<=', th_last_check), ('th_date_to', '>', th_last_check),
                         ('th_crm_level_id', '=', exist_lead.stage_id.id),
                         ('th_status_detail_id', '=', exist_lead.th_status_detail_id.id)], limit=1)
                    if check_condition:
                        if check_condition.th_result == 'keep':
                            val_return['th_dup_state'] = 'processed'
                            val_return['th_selection_dup_result'] = 'keep'
                        if check_condition.th_result == 'transfer':
                            val_return['th_dup_state'] = 'processed'
                            val_return['th_selection_dup_result'] = 'change'
                    else:
                        val_return['th_dup_state'] = 'processing'

                return val_return
        except Exception as e:
            print(e)
        return {}

    # @api.model
    # def run_opportunity_sync(self, res=True):
    #     # Tạm thời bỏ schedule này bởi đã đồng bộ realtime
    #     return True
    #     server_api = self.env['th.api.server'].search([('state', '=', 'deploy'), ('th_type', '=', 'aff')], limit=1,
    #                                                   order='id desc')
    #     if not server_api:
    #         return False
    #     try:
    #         result_apis = xmlrpc.client.ServerProxy('{}/xmlrpc/2/object'.format(server_api.th_url_api))
    #         db = server_api.th_db_api
    #         uid_api = server_api.th_uid_api
    #         password = server_api.th_password
    #         data = result_apis.execute_kw(db, uid_api, password, 'th.opportunity.ctv', 'th_action_synchronize_data', [[], res])
    #
    #         start_time = datetime.now().replace(hour=0, minute=0, second=0, microsecond=0) - timedelta(days=1)
    #         crm_lead = self.env['crm.lead'].sudo().search([('th_affiliate_code', 'in', data)])
    #         if crm_lead:
    #             affiliate_codes = crm_lead.mapped('th_affiliate_code')
    #             data1 = self.setup_data_crm(data_code_aff=affiliate_codes)
    #         return data1
    #     except Exception as e:
    #         raise ValidationError(e)

    # Chỉ xóa khi có context(force_delete)
    def unlink(self):
        for rec in self:
            # rec.with_context(is_delete=True).setup_data_crm(rec)
            if len(rec.partner_id.opportunity_ids) <= 1:
                rec.partner_id.th_check_module = True
        return super(CrmLead, self).unlink()

    # def setup_data_crm(self, res=None):
    #     values_crm = {}
    #     try:
    #         if not res:
    #             raise ValidationError('Không có bản ghi!')
    #         server_api = self.env['th.api.server'].search([('state', '=', 'deploy'), ('th_type', '=', 'aff')], limit=1, order='id desc')
    #         if not server_api:
    #             # return False
    #             raise ValidationError('Không có tìm thấy server!')
    #
    #         result_apis = xmlrpc.client.ServerProxy('{}/xmlrpc/2/object'.format(server_api.th_url_api))
    #         db = server_api.th_db_api
    #         uid_api = server_api.th_uid_api
    #         password = server_api.th_password
    #         data_crm = []
    #         context = {'th_test_import': True}
    #         for rec in res:
    #             if rec.partner_id and not rec.partner_id.th_partner_aff_id:
    #                 rec.partner_id.th_action_create_partner_aff(rec.partner_id)
    #             if rec.th_partner_referred_id and not rec.th_partner_referred_id.th_partner_aff_id:
    #                 rec.th_partner_referred_id.th_action_create_partner_aff(rec.th_partner_referred_id)
    #             values_crm = {
    #                 'th_ownership_id': rec.th_ownership_id.th_own_b2b_id if rec.th_ownership_id.th_own_b2b_id else False,
    #                 'th_origin_id': rec.th_origin_id.th_origin_b2b_id if rec.th_origin_id.th_origin_b2b_id else False,
    #                 'th_source_name': rec.th_source_name if rec.th_source_name else False,
    #                 'th_description': rec.th_description if rec.th_description else False,
    #                 'th_major_id': rec.th_major_id.th_major_b2b_id if rec.th_major_id.th_major_b2b_id else False,
    #                 'type': 'opportunity',
    #                 'th_status_group_id': rec.th_status_group_id.th_s_c_b2b_id if rec.th_status_group_id.th_s_c_b2b_id else False,
    #                 'th_status_detail_id': rec.th_status_detail_id.th_s_c_d_b2b_id if rec.th_status_detail_id.th_s_c_d_b2b_id else False,
    #                 'th_channel_id': rec.th_channel_id.th_channel_b2b_id if rec.th_channel_id.th_channel_b2b_id else False,
    #                 'th_source_group_id': rec.th_source_group_id.th_source_b2b_id if rec.th_source_group_id.th_source_b2b_id else False,
    #                 'stage_id': rec.stage_id.th_stage_b2b_id if rec.stage_id.th_stage_b2b_id else False,
    #                 'th_lead_aff_id': rec.id,
    #                 'state': rec.state if rec.state else False,
    #                 'th_last_check': rec.th_last_check if rec.th_last_check else False,
    #                 'partner_id': rec.partner_id.th_partner_aff_id if rec.partner_id.th_partner_aff_id else False,
    #                 'th_partner_referred_id': rec.th_partner_referred_id.th_partner_aff_id if rec.th_partner_referred_id.th_partner_aff_id else False,
    #                 'th_admissions_station_id': rec.th_admissions_station_id.th_station_b2b_id if rec.th_admissions_station_id.th_station_b2b_id else False,
    #                 'th_admissions_region_id': rec.th_admissions_region_id.th_region_b2b_id if rec.th_admissions_region_id.th_region_b2b_id else False,
    #                 'th_level_up_date': rec.th_level_up_date if rec.th_level_up_date else False,
    #                 'th_person_in_charge': rec.user_id.name if rec.user_id.name else False,
    #                 'th_lead_crm_samp_id': rec.id,
    #                 'th_graduation_system_id': rec.th_graduation_system_id.th_graduation_b2b_id if rec.th_graduation_system_id else False,
    #                 'th_utm_source': rec.th_utm_source,
    #                 'th_utm_medium': rec.th_utm_medium,
    #                 'th_utm_campaign': rec.th_utm_campaign,
    #                 'th_utm_term': rec.th_utm_term,
    #                 'th_utm_content': rec.th_utm_content,
    #                 'th_crm_job': rec.th_crm_job,
    #             }
    #
    #             th_lead_aff = result_apis.execute_kw(db, uid_api, password, 'crm.lead', 'search', [[['id', '=', rec.th_crm_lead_b2b_id]]])
    #             if not th_lead_aff and rec.th_crm_lead_b2b_id:
    #                 return True
    #
    #             if not rec.th_crm_lead_b2b_id and not self._context.get('is_delete'):
    #                 context['name'] = rec.name
    #                 lead_id = result_apis.execute_kw(db, uid_api, password, 'crm.lead', 'create', [values_crm], {'context': context})
    #                 if lead_id:
    #                     rec.with_context(th_test_import=True).write({'th_crm_lead_b2b_id': lead_id})
    #             elif rec.th_crm_lead_b2b_id:
    #                 if not self._context.get('is_delete', False):
    #                     lead_id = result_apis.execute_kw(db, uid_api, password, 'crm.lead', 'write', [[rec.th_crm_lead_b2b_id], values_crm], {'context': context})
    #                 else:
    #                     lead_id = result_apis.execute_kw(db, uid_api, password, 'crm.lead', 'unlink', [[rec.th_crm_lead_b2b_id]])
    #     except Exception as e:
    #         print(e)
    #         self.env['th.log.api'].create({
    #             'state': 'error',
    #             'th_model': str(self._name),
    #             'th_description': str(e),
    #             'th_record_id': str(res.ids),
    #             'th_input_data': str(values_crm),
    #             'th_function_call': str('setup_data_crm'),
    #         })
    #         return True

    # def action_refresh_level(self):
    #     for res in self:
    #         if res.order_ids:
    #             for rec in res.order_ids:
    #                 if rec.opportunity_id and rec.opportunity_id.th_auto_next_level():
    #                     rec.with_user(self.env.ref('base.user_root')).opportunity_id.sudo().stage_id = rec.opportunity_id.th_auto_next_level().th_crm_level.id
    #                 for inv in rec.invoice_ids:
    #                     if inv.th_payment_status in ['partial', 'paid', 'over_payment'] and not inv.th_receive_amount and inv.move_type == 'out_invoice':
    #                         inv.th_receive_amount = inv.amount_total
    #                     elif inv.th_payment_status in ['partial', 'paid', 'over_payment'] and not inv.th_refund_amount and inv.move_type == 'out_refund':
    #                         inv.th_refund_amount = inv.amount_total
    #         if res.th_student_profile_id and res.th_auto_next_level():
    #             res.with_user(self.env.ref('base.user_root')).sudo().stage_id = res.th_auto_next_level().th_crm_level.id

# tạo mới đơn hàng từ form cơ hội CRM,mặc định tạo đơn sẽ là đơn tổng hợp, có thể tạo nhiều đơn hàng
    def action_sale_quotations_new(self):
        domain = json.dumps([('categ_id', 'in', self.env['product.category'].search(
            [('th_module_ids', 'in', self.env.ref('th_setup_parameters.th_crm_module').ids)]).ids)])
        res = super().action_sale_quotations_new()
        res['context']['create_invoice'] = True
        res['target'] = 'new'
        res['context']['default_th_sale_order'] = 'crm'
        res['context']['default_th_domain'] = domain
        res['context']['default_th_type_order'] = 'summary'
        self.th_hide_action_confirm_crm = True
        # if self.sudo().order_ids:
        #     raise ValidationError("Đã có đơn hàng, vui lòng không tạo thêm!")
        return res

    def action_view_sale_order(self):
        res = super().action_view_sale_order()
        return res

    def th_action_assign_user(self):
        view_id = self.env.ref('th_crm.th_lead_reuse_view_form2').id
        context = self.env.context.copy()
        context.update({'default_th_create_reuse': True})
        # self.env['crm.lead.reuse'].browse(self._context.get('active_id')).th_create_reuse = True
        return {
            'name': 'Chia cơ hội',
            'view_mode': 'form',
            'res_model': 'crm.lead.reuse',
            'type': 'ir.actions.act_window',
            'target': 'new',
            'views': [[view_id, 'form']],
            # 'res_id': self._context.get('active_id'),
            'context': context,
        }

    def update_th_last_check(self):
        self.update({'th_last_check': fields.Datetime.now()})

    def th_create_activities(self):
        view_id = self.env.ref('mail.mail_activity_view_form_popup').id
        model = self.env['ir.model'].search([('model', '=', 'crm.lead')])
        return {
            'name': 'Lịch làm việc',
            'view_mode': 'form',
            'res_model': 'mail.activity',
            'type': 'ir.actions.act_window',
            'views': [[view_id, 'form']],
            'target': 'new',
            'context': {'default_res_model_id': model.id,
                        'default_res_id': self[0].id,
                        'default_user_id': self[0].user_id.id,
                        'create_activities': True}
        }

# Nhảy level tự động
    def th_auto_next_level(self):
        for rec in self:
            domain = []
            th_type_condition = 'no_order'
            accounts = self.env['account.move'].sudo().search([('th_crm_lead_id', '=', self.id)])
            sale_order = self.env['sale.order'].search([('opportunity_id', '=', self.id)])
            level = self.env['th.level.condition']
            if rec.sudo().stage_id.is_won:
                return level
            # kiểm tra hóa đơn
            if not accounts:
                th_type_condition = 'no_order'
            else:
                if any(accounts.filtered(
                    lambda d: d.th_payment_status in ['partial', 'paid'] and d.move_type == 'out_refund')):
                    return level
                # Kiểm tra khi có đơn tổng hợp
                if any(sale_order.filtered(lambda d: d.th_type_order == 'summary')):
                    if accounts and len(accounts.filtered(lambda d: d.th_payment_status in ('paid', 'over_payment'))) == len(accounts):
                        th_type_condition = 'handed_over_full'
                    elif any(accounts.filtered(lambda d: d.th_payment_status in ('partial', 'paid', 'over_payment'))):
                        th_type_condition = 'handed_over_partial_tuition'
                    elif all(accounts.filtered(lambda d: d.th_payment_status in ['not_paid', False] and d.move_type == 'out_invoice')):
                        th_type_condition = 'no_order'
                # Kiểm tra khi ko có đơn tổng hợp
                elif not any(sale_order.filtered(lambda d: d.th_type_order == 'summary')):
                    # khi có đủ đơn hàng học phí + lệ phí
                    if any(sale_order.filtered(lambda d: d.th_type_order == 'tuition')) and any(sale_order.filtered(lambda d: d.th_type_order == 'fee')):
                        if accounts and len(accounts.filtered(lambda d: d.th_payment_status in ('paid', 'over_payment'))) == len(accounts):
                            th_type_condition = 'handed_over_full'
                        elif any(accounts.filtered(
                                lambda d: d.th_payment_status in ('partial', 'paid', 'over_payment'))):
                            th_type_condition = 'handed_over_partial_tuition'
                        elif all(accounts.filtered(
                                lambda d: d.th_payment_status in ['not_paid', False] and d.move_type == 'out_invoice')):
                            th_type_condition = 'no_order'
                    # Khi chưa đủ đơn học phí + lệ phí
                    else:
                        if any(accounts.filtered(lambda d: d.th_payment_status in ('partial', 'paid', 'over_payment'))):
                            th_type_condition = 'handed_over_partial_tuition'
                        elif all(accounts.filtered(lambda d: d.th_payment_status in ['not_paid', False] and d.move_type == 'out_invoice')):
                            th_type_condition = 'no_order'

            domain.append(('th_type_condition', '=', th_type_condition))
            # kiểm tra hồ sơ
            if not rec.th_student_profile_id:
                domain.append(('th_no_profile', '=', True))
            else:
                domain.append(('th_no_profile', '=', False))
            if rec.th_student_profile_id.th_profile_status in ('missing', 'minimum'):
                domain.append(('th_missing_profile', '=', True))
                if rec.th_student_profile_id.th_handover_status == 'handed_over' or rec.th_check_admission:
                    domain.append(('th_enough_profile', '=', True))
                    domain.append(('th_handed_over_profile', '=', True))
                else:
                    domain.append(('th_enough_profile', '=', False))
                    domain.append(('th_handed_over_profile', '=', False))
            elif rec.th_student_profile_id.th_profile_status == 'full':
                domain.append(('th_enough_profile', '=', True))
                if rec.th_student_profile_id.th_handover_status == 'handed_over' or rec.th_check_admission:
                    domain.append(('th_handed_over_profile', '=', True))
                else:
                    domain.append(('th_handed_over_profile', '=', False))
            else:
                domain.append(('th_missing_profile', '=', False))
                domain.append(('th_enough_profile', '=', False))
                domain.append(('th_handed_over_profile', '=', False))
            if rec.th_admission_decision:
                domain.append(('th_admission_decision', '=', True))
            else:
                domain.append(('th_admission_decision', '=', False))
            if rec.th_enrollment_list:
                domain.append(('th_enrollment_list', '=', True))
            else:
                domain.append(('th_enrollment_list', '=', False))
            level |= level.search(domain, limit=1)
            return level

    def th_check_condition_lead(self, old_lead, new_lead):
        des, vals, vals_new, vals_old, vals_history = '', {}, {}, {}, {}
        try:
            today = fields.Date.today()
            old_lead = old_lead.sudo()
            if not old_lead or not new_lead:
                raise ValidationError('Không có giá trị: ' + str({'old_lead': old_lead, 'new_lead': new_lead}))
            if not old_lead.th_last_check or not new_lead.th_last_check:
                raise ValidationError('Không có giá trị ngày: ' + str({'old_th_last_check': old_lead.th_last_check, 'new_th_last_check': new_lead.th_last_check}))
            th_last_check = (fields.Datetime.now() - old_lead.th_last_check)
            th_last_check = th_last_check.total_seconds() / (24 * 60 * 60)
            th_last_check = round(th_last_check,2)
            check_condition = self.env['th.check.condition'].search(
                [('th_date_from', '<=', th_last_check), ('th_date_to', '>', th_last_check),
                 ('th_crm_level_id', '=', old_lead.stage_id.id),
                 ('th_status_detail_id', '=', old_lead.th_status_detail_id.id)], limit=1).th_result
            data = []
            old_lead = old_lead.with_context(crm_create=True)
            new_lead = new_lead.with_context(crm_create=True)
            self = self.with_context(check_condition=check_condition)
            old_lead.th_date_lead_again = today
            if check_condition and not (old_lead.sudo().th_is_close_lead or old_lead.sudo().stage_id.is_won):
                if check_condition == 'keep':
                    new_lead.sudo().with_context(crm_create=True).th_is_a_duplicate_opportunity = True
                    des += Markup(
                        f"\n Cơ hội {new_lead.name}:\n" + f"Ngày trùng {new_lead.create_date.strftime('%d/%m/%Y')} \n" + f"Mô tả {new_lead.th_description}" if new_lead.th_description else '')
                    for new in new_lead:
                        # th_lead_samp_id_old:Thắng, th_lead_samp_id_new:Thua, tạo lịch sử của con mới vào
                        vals_history = {'th_name_lead_lose': new.name,
                                        'th_ownership_id': new.th_ownership_id.id,
                                        'th_status_group_id': new.th_status_group_id.id,
                                        'th_status_detail_id': new.th_status_detail_id.id,
                                        'th_stage_id': new.stage_id.id,
                                        'th_description': new.th_description,
                                        'th_major_id': new.th_major_id.id,
                                        'th_lead_samp_id_old': new.id,
                                        'th_lead_samp_id_new': old_lead.id,
                                        'th_graduation_system_id': new.th_graduation_system_id.id,
                                        'th_admissions_station_id': new.th_admissions_station_id.id,
                                        'th_admissions_region_id': new.th_admissions_region_id.id,
                                        'th_source_name': new.th_source_name,
                                        'th_channel_id': new.th_channel_id.id,
                                        'th_source_group_id': new.th_source_group_id.id,
                                        'th_form_name': new.th_form_name,
                                        'th_uuid_form': new.th_uuid_form,
                                        'th_partner_referred_id': new.th_partner_referred_id.id,
                                        'th_registration_date': new.th_registration_date,
                                        # 'th_last_check': new.th_last_check,
                                        'state': new.state,
                                        'th_lead_b2b_id': new.th_crm_lead_b2b_id,
                                        'th_dividing_ring_id': new.th_dividing_ring_id.id,
                                        'user_id': new.user_id.id,
                                        'th_self_lead': new.th_self_lead,
                                        }
                    # vals_new = {'th_dup_state': 'processed', 'th_selection_dup_result': 'change',
                    #             'th_crm_lead_source_id': old_lead.id}
                if check_condition == 'transfer':
                    old_lead.sudo().with_context(crm_create=True).th_is_a_duplicate_opportunity = True
                    old_lead.sudo().with_context(crm_create=True).th_student_profile_id.sudo().active = False
                    if old_lead.th_storage:
                        old_lead.sudo().with_context(crm_create=True).th_storage = False
                    old_note_ids = old_lead.sudo().message_ids
                    # lognote cũ thêm vào con mới thắng
                    for note in old_note_ids:
                        note_new = note.copy()
                        note_new = note_new.with_context(crm_create=True)
                        note_new.sudo().date = note.sudo().date
                        note_new.sudo().res_id = new_lead.id
                        track_list = []
                        if note.tracking_value_ids:
                            for tracking in note.tracking_value_ids:
                                new_track_value = tracking.copy()
                                track_list.append(new_track_value.id)
                        note_new.sudo().tracking_value_ids = [(6, 0, track_list)]
                    msg1 = ("Cơ hội mới vào theo check trùng ma trận, cơ hội này đã bị xử thua!")
                    msg2 = ("Cơ hội mới vào theo check trùng ma trận - cơ hội được xử thắng!")
                    old_lead.with_context(th_test_import=True, crm_create=True).with_user(
                        self.env.ref('base.user_root')).sudo().message_post(body=msg1)
                    ccs_lead = self.env['ccs.lead'].search([('th_lead_id', '=', old_lead.id)],limit=1)
                    if ccs_lead:
                        ccs_lead.with_user(self.env.ref('base.user_root')).sudo().message_post(body=msg1)
                    new_lead.with_context(th_test_import=True, crm_create=True).with_user(
                        self.env.ref('base.user_root')).sudo().message_post(body=msg2)
                    new_lead.sudo().th_first_create_day = old_lead.sudo().create_date.date()
                    if new_lead.th_dividing_ring_id and not new_lead.user_id:
                        new_lead.sudo().user_id = new_lead.sudo().th_dividing_ring_id.action_assign_leads_dividing_ring()
                    # aff
                    # vals_old = {'th_dup_state': 'processed', 'th_selection_dup_result': 'change',
                    #             'th_crm_lead_source_id': old_lead.id}
                    # vals_new = {'th_dup_state': 'processed', 'th_selection_dup_result': 'keep',
                    #             'th_crm_lead_source_id': old_lead.id}
                    # vals = {'th_major_ids': [(6, 0, new_lead.th_major_ids.ids)],
                    #         'stage_id': new_lead.stage_id.id,
                    #         'th_major_id': new_lead.th_major_id.id,
                    #         'th_status_group_id': new_lead.th_status_group_id.id,
                    #         'th_status_detail_id': new_lead.th_status_detail_id.id,
                    #         'th_graduation_system_id': new_lead.th_graduation_system_id.id,
                    #         'th_admissions_station_id': new_lead.th_admissions_station_id.id,
                    #         'th_admissions_region_id': new_lead.th_admissions_region_id.id,
                    #         'th_channel_id': new_lead.th_channel_id.id,
                    #         'th_source_group_id': new_lead.th_source_group_id.id,
                    #         'th_form_name': new_lead.th_form_name,
                    #         'th_uuid_form': new_lead.th_uuid_form,
                    #         'th_partner_referred_id': new_lead.th_partner_referred_id.id,
                    #         'th_registration_date': new_lead.th_registration_date,
                    #         'th_last_check': new_lead.th_last_check,
                    #         'th_source_name': new_lead.th_source_name,
                    #         'state': new_lead.state,
                    #         'th_storage': False,
                    #         'th_dividing_ring_id': new_lead.th_dividing_ring_id.id,
                    #         'user_id': new_lead.user_id.id,
                    #         'th_self_lead': new_lead.th_self_lead,
                    #         }
                    if new_lead.th_description:
                        des += Markup(f"\n Mô tả của cơ hội {new_lead.name}:\n" + new_lead.th_description)
                    for old in old_lead:
                        vals_history = {'th_name_lead_lose': old.name,
                                        'th_ownership_id': old.th_ownership_id.id,
                                        'th_status_group_id': old.th_status_group_id.id,
                                        'th_status_detail_id': old.th_status_detail_id.id,
                                        'th_stage_id': old.stage_id.id,
                                        'th_description': old.th_description,
                                        'th_source_name': old.th_source_name,
                                        'th_major_id': old.th_major_id.id,
                                        'th_lead_samp_id_old': old.id,
                                        'th_lead_samp_id_new': new_lead.id,
                                        'th_graduation_system_id': old.th_graduation_system_id.id,
                                        'th_admissions_station_id': old.th_admissions_station_id.id,
                                        'th_admissions_region_id': old.th_admissions_region_id.id,
                                        'th_channel_id': old.th_channel_id.id,
                                        'th_source_group_id': old.th_source_group_id.id,
                                        'th_form_name': old.th_form_name,
                                        'th_uuid_form': old.th_uuid_form,
                                        'th_partner_referred_id': old.th_partner_referred_id.id,
                                        'th_registration_date': old.th_registration_date,
                                        'th_last_check': old.th_last_check,
                                        'state': old.state,
                                        'th_lead_b2b_id': old.th_crm_lead_b2b_id,
                                        'th_dividing_ring_id': old.th_dividing_ring_id.id,
                                        'user_id': old.user_id.id,
                                        'th_self_lead': old.th_self_lead,
                                        }
                # data.append([new_lead.th_crm_lead_b2b_id, vals_new])
                # data.append([old_lead.th_crm_lead_b2b_id, vals_old])
                #
                # vals['th_description'] = old_lead.th_description if not des else Markup(
                #     old_lead.th_description or '') + des
                # vals['th_ownership_id'] = ownership
                # vals['th_duplicate_processed_lead'] = True
                # vals['th_create_user_checked_id'] = self.env.user.id
                # vals['th_registration_date'] = new_lead.create_date if check_condition == 'transfer' else old_lead.th_registration_date
                # vals['th_last_check'] = fields.Date.today() if check_condition == 'transfer' else old_lead.th_last_check
                # vals['th_crm_lead_b2b_id'] = th_crm_lead_b2b_id
                # old_lead.with_context(th_test_import=True, crm_create=True).sudo().write(vals)
                new_lead.with_context(th_test_import=True, crm_create=True).sudo().write({'th_duplicate_processed_lead': True,
                                                                                          'th_description': old_lead.th_description if not des else (Markup(old_lead.th_description or '') + des)
                                                                                          })
                if vals_history and not self.env['th.duplicate.check.history'].search(
                        [('th_lead_samp_id_old', '=', old_lead.id), ('th_lead_samp_id_new', '=', new_lead.id), ]):
                    self.env['th.duplicate.check.history'].create(vals_history)
            else:
                if old_lead.th_is_close_lead or old_lead.stage_id.is_won:
                    new_lead.with_context(th_test_import=True, crm_create=True).sudo().write({'th_dup_need_admin': True,
                                                                                          'th_is_a_duplicate_opportunity':True,
                                                                                          'th_duplicate_type': 'no_results'})
                new_lead.with_context(th_test_import=True, crm_create=True).sudo().write({'th_is_a_duplicate_opportunity': True,
                                                                                          'th_duplicate_type': 'no_results'})
                # new_lead.with_context(th_test_import=True, crm_create=True).sudo().write({'th_is_a_duplicate_opportunity':True})
                # new_lead.with_context(th_test_import=True, crm_create=True).sudo().write({'th_duplicate_type': 'no_results'})
                # data.append(
                #     [new_lead.th_crm_lead_b2b_id, {'th_dup_state': 'processing', 'th_crm_lead_source_id': old_lead.id}])
                # data.append(
                #     [old_lead.th_crm_lead_b2b_id, {'th_dup_state': 'processing', 'th_crm_lead_source_id': old_lead.id}])
            # if data and new_lead.th_ownership_id.th_is_sync:
            #     # return True
            #     server_api = self.env['th.api.server'].search([('state', '=', 'deploy'), ('th_type', '=', 'aff')], limit=1, order='id desc')
            #     if not server_api:
            #         return False
            #     result_apis = xmlrpc.client.ServerProxy('{}/xmlrpc/2/object'.format(server_api.th_url_api))
            #     db = server_api.th_db_api
            #     uid_api = server_api.th_uid_api
            #     password = server_api.th_password
            #     lead_id = result_apis.execute_kw(db, uid_api, password, 'crm.lead', 'th_write_condition', [[], data])
                return
        except Exception as e:
            print(e)
            self.env['th.log.api'].create({
                'state': 'error',
                'th_model': str(self._name),
                'th_description': str(e),
                'th_record_id': str({'old_lead': old_lead, 'new_lead': new_lead}),
                'th_input_data': str({'vals': vals, 'vals_new': vals_new, 'vals_old': vals_old}),
                'th_function_call': str('th_check_condition_lead'),
            })
            return

    def th_action_create_new_lead(self):
        for rec in self:
            rec.th_is_a_duplicate_opportunity = False
            rec.th_dup_need_admin = False
            rec.th_is_apply_lead = True
            msg = (" Cơ hội này đã được duyệt tạo mới")
            rec.message_post(body=msg)

    def action_th_product_template(self):
        action = self.env['ir.actions.actions']._for_xml_id('th_crm.th_product_template_action_crm')
        crm_module = self.env.ref('th_setup_parameters.th_crm_module').ids
        product_categ_id = self.env['product.category'].search([('th_module_ids', 'in', crm_module)])
        action['domain'] = [('categ_id', 'in', product_categ_id.ids)]
        domain = json.dumps([('id', 'in', self.env['product.category'].search([('th_module_ids', 'in',  self.env.ref('th_setup_parameters.th_crm_module').ids)]).ids)])
        action['context'] = {'default_th_crm_product_category_domain': domain,'view_product_crm': True, 'default_categ_id': False,'search_default_filter_to_sell': 1}
        return action

    @api.model
    def receive_data_from_module(self, data_to_send):
        check_phone = self.env['res.partner'].search(
            [('phone', '=', data_to_send.get('th_phone_number')), ('email', '=', data_to_send.get('th_mail'))],
            limit=1)
        affiliate_code = self.env['res.partner'].search([('name', '=', data_to_send.get('th_affiliate_code'))], limit=1)
        if check_phone:
            self.env['crm.lead'].create([{'partner_id': check_phone.id,
                                          'th_description': data_to_send.get('th_description'),
                                          'th_partner_referred_id': affiliate_code.id, }])
        elif not check_phone:
            id_partner = self.env['res.partner'].create([{'name': data_to_send.get('name'),
                                                          'phone': data_to_send.get('th_phone_number'),
                                                          'email': data_to_send.get('th_mail')}])

            self.env['crm.lead'].create([{'partner_id': id_partner.id,
                                          'th_description': data_to_send.get('th_description'),
                                          'th_partner_referred_id': affiliate_code.id, }])

    @api.model
    def th_action_deduplicate(self):
        rec_duplicate = self.sudo().env['data_merge.model'].search([('res_model_id.model', '=', 'crm.lead')])
        if rec_duplicate:
            rec_duplicate.th_action_find_duplicates()
            action = self.sudo().env["ir.actions.actions"]._for_xml_id("th_crm.th_action_data_merge_record_notification")
            return action
        else:
            raise ValidationError("Đã có lỗi xảy ra. Vui lòng liên hệ Admin để xử lý cấu hình")

    @api.onchange('stage_id')
    def onchange_status_group(self):
        for rec in self:
            if rec.th_status_group_id not in rec.env['th.status.category'].search(
                    [('th_type', '=', 'crm'), ('th_crm_level_ids', '=', rec.stage_id.id)]):
                rec.th_status_group_id = False

    @api.onchange('stage_id')
    def _compute_th_level_up_date(self):
        for rec in self:
            rec.th_level_up_date = fields.Date.today()

    def _compute_readonly_domain(self):
        for rec in self:
            if not self._context.get('view_b2b', False):
                rec.readonly_domain = json.dumps(['|', ('th_is_a_duplicate_opportunity', '=', True), ('th_is_close_lead', '=', True)])
            else:
                rec.readonly_domain = False

    def action_open_profile_customer(self):
        self.ensure_one()
        return {
            'name': 'Hồ sơ',
            'view_mode': 'form',
            'res_model': 'res.partner',
            'type': 'ir.actions.act_window',
            'target': 'new',
            'res_id': self.partner_id.id,
            'domain': [('id', '=', self.partner_id.id)],
        }

    @api.depends('partner_id')
    def _compute_th_opportunity_list_partner_crm_ids(self):
        for record in self:
            list_ids = []
            record.th_opportunity_list_partner_crm_ids = False
            if record.partner_id:
                opportunities = self.env['crm.lead'].sudo().search([
                                                             ('partner_id', '=', record.partner_id.id),
                                                             ('th_origin_id', '!=', record.th_origin_id.id),
                                                             ('th_is_a_duplicate_opportunity', '!=', True),])
                for rec in opportunities:
                    opportunity_list_id = self.env['crm.lead.opportunity.list.partner'].sudo().create({
                        'name': rec.name if rec.name else "Mới",
                        'th_partner_id': rec.partner_id.id,
                        'th_last_check': rec.th_last_check,
                        'th_origin_id': rec.th_origin_id.id,
                        'th_stage_id': rec.stage_id.id
                    })
                    list_ids.append(opportunity_list_id.id)
                record.th_opportunity_list_partner_crm_ids = [(6, 0, list_ids)]

    def action_view_history(self):
        self.ensure_one()
        crm_id = self.sudo().search([('partner_id', '=', self.partner_id.id), ('th_origin_id', '=', self.th_origin_id.id),
                                     ('id', '!=', self.id), ('th_is_a_duplicate_opportunity', '=', False), ('type', '=', 'opportunity')], order='id asc')
        if len(crm_id) > 1:
            crm_id = self.env['crm.lead'].sudo().search(
                [('id', '!=', self.id), ('th_origin_id', '=', self.th_origin_id.id),
                 ('partner_id', '=', self.partner_id.id), ('th_is_a_duplicate_opportunity', '=', False),
                 ('th_is_close_lead', '=', False)])
            if not crm_id:
                crm_id = self.env['crm.lead'].sudo().search(
                    [('id', '!=', self.id), ('th_origin_id', '=', self.th_origin_id.id),
                     ('partner_id', '=', self.partner_id.id), ('th_is_a_duplicate_opportunity', '=', False)], limit=1)
        for crm in crm_id:
            if not self.env['th.care.history'].search([('th_crm_lead_old_id', '=', crm.id), ('th_crm_lead_new_id', '=', self.id)]):
                self.env['th.care.history'].create({'th_crm_lead_old_id': crm.id,
                                                    'th_crm_lead_new_id': self.id,})
        return {
            'name': 'Lịch sử chăm sóc',
            'view_mode': 'tree,form',
            'res_model': "th.care.history",
            'type': 'ir.actions.act_window',
            'target': 'target',
            'domain': [('th_crm_lead_new_id', '=', self.id)],
        }

    def th_send_noti_duplicate_lead(self):
        self.ensure_one()
        duplicate = self.env['th.duplicate.lead.notify.wr'].search([('new_lead_id', '=', self.id)])
        if self.th_dup_need_admin:
            raise ValidationError("Cơ hội đang cần admin xử lý, không được phép gửi khiếu nại!")
        if not duplicate:
            return {
                'name': 'Báo trùng',
                'view_mode': 'form',
                'res_model': "th.duplicate.lead.notify.wr",
                'type': 'ir.actions.act_window',
                'target': 'new',
                'context': {'default_new_lead_id': self.id}
            }
        else:
            return {
                'name': 'Báo trùng',
                'view_mode': 'form',
                'res_model': "th.duplicate.lead.notify.wr",
                'type': 'ir.actions.act_window',
                'target': 'new',
                'res_id': duplicate.id,
                'context': {'default_new_lead_id': self.id}
            }

    def _message_add_suggested_recipient(self, result, partner=None, email=None, lang=None, reason=''):
        """ Called by _message_get_suggested_recipients, to add a suggested
            recipient in the result dictionary. The form is :
                partner_id, partner_name<partner_email> or partner_name, reason """
        result = super()._message_add_suggested_recipient(result, partner=partner, email=email, lang=lang, reason=reason)
        # Áp dụng với crm không gửi emai cho khách hàng
        if self._name == 'crm.lead':
            for res in range(len(result.get(self.id))):
                if result.get(self.id)[res][0] == self.partner_id.id:
                    del result.get(self.id)[res]
        return result

    def th_action_crm_c1(self, vals):
        domain = []
        partner_aff = False
        th_partner_referred_id = False
        if vals.get('state', False) and vals.get('state', False) == 'transfer' and not vals.get('team_id', False):
            vals['team_id'] = self.env.ref('th_crm.th_team_group_in_charge').id

        if not self._context.get('write', False) and not self._context.get('id', False):
            if not vals.get('partner_id', False):
                if self._context.get('email', False):
                    domain.append(('email', '=', self._context.get('email', False)))
                if self._context.get('phone', False):
                    domain.append(('phone', '=', self._context.get('phone')))
                if self._context.get('th_phone2', False):
                    domain.append(('th_phone2', '=', self._context.get('th_phone2')))
            else:
                partner_aff = self.env['res.partner'].search([('id', '=',  vals.get('partner_id', False))], limit=1)

            if domain:
                partner_aff = self.env['res.partner'].search(domain, limit=1)

            if not partner_aff:
                vals['th_check_module'] = True
                partner_aff = self.env['res.partner'].create({
                    'name': self._context.get('partner_name', False),
                    'phone': self._context.get('phone', False),
                    'email': self._context.get('email', False),
                    'th_phone2': self._context.get('th_phone2', False),
                    'th_partner_aff_id': self._context.get('partner_aff_id'),
                })
            if not partner_aff:
                return False

            if self._context.get('th_affiliate_code', False):
                th_partner_referred_id = self.env['res.partner'].search([('th_affiliate_code', '=', self._context.get('th_affiliate_code'))], limit=1).id

            vals['partner_id'] = partner_aff.id
            vals['th_partner_referred_id'] = th_partner_referred_id

            try:
                values = {}
                exist_lead = self.env['crm.lead'].search(
                    [('th_origin_id', '=', vals.get('th_origin_id', 0)),
                     ('partner_id', '=', vals.get('partner_id', 0)), ('th_is_a_duplicate_opportunity', '=', False)],
                    limit=1)
                if exist_lead:
                    e_date = exist_lead.th_last_check.date()
                    e_stage = exist_lead.stage_id.id
                    e_status_detail = exist_lead.th_status_detail_id.id

                crm_lead = self.sudo().create(vals)
                if vals.get('th_crm_lead_b2b_id', False) and crm_lead:
                    values.update({
                        'partner_id': crm_lead.partner_id.id,
                        'th_customer_code': crm_lead.partner_id.th_customer_code,
                        'th_affiliate_code': crm_lead.partner_id.th_affiliate_code,
                        'crm_id': crm_lead.id,
                        'th_person_in_charge': crm_lead.user_id.name if crm_lead.user_id else False,
                        'name': crm_lead.name,
                    })

                # check trùng
                for rec in crm_lead:
                    if not exist_lead:
                        return values

                    values['th_duplicate_processed_lead'] = True
                    th_last_check = (fields.Date.today() - e_date).days
                    check_condition = self.env['th.check.condition'].search(
                        [('th_date_from', '<=', th_last_check), ('th_date_to', '>', th_last_check),
                         ('th_crm_level_id', '=', e_stage),
                         ('th_status_detail_id', '=', e_status_detail)], limit=1).th_result
                    # check_condition = self._context.get('check_condition', False)
                    if check_condition:
                        if check_condition == 'keep':
                            values['th_dup_state'] = 'processed'
                            values['th_selection_dup_result'] = 'change'
                            values['th_crm_lead_source_id'] = exist_lead.id
                        if check_condition == 'transfer':
                            values['th_dup_state'] = 'processed'
                            values['th_selection_dup_result'] = 'keep'
                            values['th_lead_crm_samp_id'] = exist_lead.id
                            values['th_crm_lead_source_id'] = exist_lead.id
                    else:
                        values['th_dup_state'] = 'processing'
                        values['th_crm_lead_source_id'] = exist_lead.id

                return values
            except Exception as e:
                print(e)
                return {}
        else:
            try:
                data = self.browse(self._context.get('id')).with_context(th_test_import=True).sudo().write(vals)
                crm_lead = self.browse(self._context.get('id'))
                return {
                    'partner_id': crm_lead.partner_id.id,
                    'th_customer_code': crm_lead.partner_id.th_customer_code,
                    'th_affiliate_code': crm_lead.partner_id.th_affiliate_code,
                    'th_person_in_charge': crm_lead.user_id.name if crm_lead.user_id else False,
                }
            except Exception as e:
                print(e)
                return False

    def th_open_dup_lead(self):
        return {
            "name": _("Phân xử"),
            "type": 'ir.actions.act_window',
            "res_model": 'th.duplicate.check.history',
            "views": [[False, "tree"], [False, "form"]],
            'view_id': self.env.ref('th_crm.th_duplicate_check_history_view_tree').id,
            "target": 'new',
            "context": {},
            "domain": ['|',('th_lead_samp_id_old', '=', self.id),('th_lead_samp_id_new', '=', self.id), ('th_duplicate_type', '=', 'need_handle')],
        }

    @api.model
    def get_import_templates(self):
        res = super(CrmLead, self).get_import_templates()
        return [{
            'label': _('Mẫu Import'),

            'template': '/th_crm/static/xlsx/ImportCoHoiCRM02.xlsx'
        }]

    def th_keep_lead(self):
        for rec in self:
            rec.th_duplicate_type = 'manual'
            log_note = _("Đã phân xử thành công")
            self.message_post(body=log_note)
            self.env['bus.bus'].sudo()._sendone(
                self.env.user.partner_id,
                "simple_notification",
                {
                    "title": "Warning",
                    "message": log_note,
                    "warning": False
                })
            # gửi cho cơ hội đang chăm
            rec.activity_schedule(
                'crm_lead.th_crm_icon',
                summary='Khiếu nại CRM đã xử lý xong',
                note='Thông báo: Cơ hội %s sau khi khiếu nại đã được phân xử thắng' % rec.name,
                user_id=rec.create_uid.id,
            )
            # search các cơ hội trùng mà khiếu nại để gửi thông báo
            leads_dup_complaints = self.env['th.duplicate.check.history'].search(
                ['|', ('th_lead_samp_id_old', '=', self.id), ('th_lead_samp_id_new', '=', self.id),
                 ('th_duplicate_type', '=', 'need_handle')])
            for lead in leads_dup_complaints.mapped('th_lead_samp_id_old'):
                if lead.th_user_complaints:
                    lead.activity_schedule(
                        'crm_lead.th_crm_icon',
                        summary='Khiếu nại CRM đã xử lý xong',
                        note='Thông báo: Cơ hội khiếu nại đã được phân xử lại xong',
                        user_id=lead.th_user_complaints.id,
                    )

    # def prepare_data_sync(self, datas):
    #     data_sync = []
    #     for rec in datas:
    #         exist_lead = self.env['crm.lead'].search(
    #             [('id', '!=', rec.id), ('th_origin_id', '=', rec.th_origin_id.id),
    #              ('partner_id', '=', rec.partner_id.id), ('th_is_a_duplicate_opportunity', '=', False)],
    #             limit=1)
    #
    #         th_ownership_id = rec.th_ownership_id.th_own_b2b_id if rec.th_ownership_id else False
    #         th_origin_id = rec.th_origin_id.th_origin_b2b_id if rec.th_origin_id else False
    #         th_major_id = rec.th_major_id.th_major_b2b_id if rec.th_major_id else False
    #         th_status_group_id = rec.th_status_group_id.th_s_c_b2b_id if rec.th_status_group_id else False
    #         th_status_detail_id = rec.th_status_detail_id.th_s_c_d_b2b_id if rec.th_status_detail_id else False
    #         th_channel_id = rec.th_channel_id.th_channel_b2b_id if rec.th_channel_id else False
    #         th_source_group_id = rec.th_source_group_id.th_source_b2b_id if rec.th_source_group_id else False
    #         stage_id = rec.stage_id.th_stage_b2b_id if rec.stage_id else False
    #         th_admissions_station_id = rec.th_admissions_station_id.th_station_b2b_id if rec.th_admissions_station_id else False
    #         th_admissions_region_id = rec.th_admissions_region_id.th_region_b2b_id if rec.th_admissions_region_id else False
    #         th_graduation_system_id = rec.th_graduation_system_id.th_graduation_b2b_id if rec.th_graduation_system_id else False
    #         th_dividing_ring_id = rec.th_dividing_ring_id.th_dividing_ring_b2b_id if rec.th_dividing_ring_id else False
    #         crm_data = {
    #             'th_ownership_id': th_ownership_id,
    #             'th_origin_id': th_origin_id,
    #             'th_source_name': rec.th_source_name or False,
    #             'th_description': rec.th_description or False,
    #             'th_major_id': th_major_id,
    #             'type': 'opportunity',
    #             'th_status_group_id': th_status_group_id,
    #             'th_status_detail_id': th_status_detail_id,
    #             'th_channel_id': th_channel_id,
    #             'th_source_group_id': th_source_group_id,
    #             'stage_id': stage_id,
    #             'th_lead_crm_samp_id': rec.id,
    #             'state': rec.state or False,
    #             'th_last_check': rec.th_last_check or False,
    #             'th_admissions_station_id': th_admissions_station_id,
    #             'th_admissions_region_id': th_admissions_region_id,
    #             'th_graduation_system_id': th_graduation_system_id,
    #             'th_crm_job': rec.th_crm_job,
    #             'th_utm_source': rec.th_utm_source,
    #             'th_utm_medium': rec.th_utm_medium,
    #             'th_utm_campaign': rec.th_utm_campaign,
    #             'th_utm_term': rec.th_utm_term,
    #             'th_utm_content': rec.th_utm_content,
    #             'name': rec.name,
    #             'partner_id': rec.partner_id.th_partner_aff_id or False,
    #             'th_dividing_ring_id': th_dividing_ring_id,
    #             'th_is_a_duplicate_opportunity': rec.th_is_a_duplicate_opportunity,
    #             'th_dup_state': 'processing' if rec.th_is_a_duplicate_opportunity else False,
    #             'th_crm_lead_source_id': exist_lead.id if rec.th_is_a_duplicate_opportunity else False,
    #         }
    #
    #         partner_data = {
    #             'name': rec.partner_id.name,
    #             'phone': rec.partner_id.phone,
    #             'email': rec.partner_id.email,
    #             'th_phone2': rec.partner_id.th_phone2,
    #             'th_partner_samp_id': rec.partner_id.id,
    #             'th_customer_code': rec.partner_id.th_customer_code,
    #             'th_affiliate_code': rec.partner_id.th_affiliate_code,
    #         }
    #
    #         source_group_data = {
    #             'name': rec.th_source_group_id.name,
    #             'th_source_samp_id': rec.th_source_group_id.id
    #         }
    #
    #         dividing_ring_data = {
    #             'name': rec.th_dividing_ring_id.name,
    #             'th_dividing_ring_samp_id': rec.th_dividing_ring_id.id,
    #             'th_is_partner_dividing': rec.th_dividing_ring_id.th_is_partner_dividing,
    #             'th_origin_id': rec.th_origin_id.th_origin_b2b_id,
    #         }
    #
    #         data_sync.append([rec.th_crm_lead_b2b_id, crm_data, partner_data, source_group_data, dividing_ring_data])
    #     return data_sync

    # def th_sync_crm_samp(self, date=0, limit=None, update=True, ids=None, limit_chunk=100, th_ownership_ids=None):
    #     datas = False
    #     data_sync = []
    #     try:
    #         domain = [('type', '=', 'opportunity'), ('write_date', '>=', (datetime.now() - timedelta(days=date)).replace(hour=0, minute=0, second=0, microsecond=0))]
    #         if update:
    #             domain.append(('th_crm_lead_b2b_id', '!=', 0))
    #         if ids:
    #             domain.append(('id', 'in', ids))
    #
    #         if th_ownership_ids:
    #             domain.append(('th_ownership_id', 'in', th_ownership_ids))
    #         datas = self.env['crm.lead'].search(domain)
    #         chunk_list = [datas[i:i + limit_chunk] for i in range(0, len(datas), limit_chunk)]
    #         for chunk in chunk_list:
    #             # datas = self.search(domain, limit=limit).filtered(lambda r: r.th_ownership_id.th_is_sync)
    #             if not chunk:
    #                 raise UserError('không có chunk')
    #             data_sync = self.env['crm.lead'].prepare_data_sync(chunk)
    #             data = chunk.sudo().with_delay(priority=9).th_sync_data_crm_c1(data_sync)
    #
    #     except Exception as e:
    #         self.env['th.log.api'].create({
    #             'state': 'error',
    #             'th_model': str(self._name),
    #             'th_description': str(e),
    #             'th_record_id': str(datas),
    #             'th_input_data': str(data_sync),
    #             'th_function_call': str('th_sync_crm_samp'),
    #         })

    # def th_sync_create_crm_samp(self, date=0, limit=None, ids=None, limit_chunk=1, th_ownership_ids=None):
    #     domain = [('type', '=', 'opportunity'), ('state', '!=', 'keep'),
    #               ('write_date', '>=', (datetime.now() - timedelta(days=date)).replace(hour=0, minute=0, second=0, microsecond=0)),
    #               ('th_crm_lead_b2b_id', 'in', [0, False])
    #               ]
    #     datas = False
    #     data_sync = False
    #     if th_ownership_ids:
    #         domain.append(('th_ownership_id', 'in', th_ownership_ids))
    #     try:
    #         if ids:
    #             domain.append(('id', 'in', ids))
    #         datas = self.search(domain, limit=limit)
    #         chunk_list = [datas[i:i + limit_chunk] for i in range(0, len(datas), limit_chunk)]
    #         for chunk in chunk_list:
    #             if not chunk:
    #                 raise UserError('không có chunk')
    #             data_sync = self.env['crm.lead'].prepare_data_sync(chunk)
    #             data = chunk.sudo().with_delay(priority=9).th_sync_data_crm_c1(data_sync)
    #     except Exception as e:
    #         self.env['th.log.api'].create({
    #             'state': 'error',
    #             'th_model': str(self._name),
    #             'th_description': str(e),
    #             'th_record_id': str(datas),
    #             'th_input_data': str(data_sync),
    #             'th_function_call': str('th_sync_create_crm_samp'),
    #         })

    # def th_sync_data_crm_c1(self, data_sync):
    #     server_api = self.sudo().env['th.api.server'].search([('state', '=', 'deploy'), ('th_type', '=', 'aff')], limit=1, order='id desc')
    #     if not server_api:
    #         raise ValidationError('Không tìm thấy server!')
    #     context = {'aff_sch': True}
    #     result_apis = xmlrpc.client.ServerProxy('{}/xmlrpc/2/object'.format(server_api.th_url_api))
    #     db = server_api.th_db_api
    #     uid_api = server_api.th_uid_api
    #     password = server_api.th_password
    #     data = result_apis.execute_kw(db, uid_api, password, 'crm.lead', 'th_create_crm_c1_sch', [[], data_sync], {'context': context})
    #     for rec in data:
    #         if rec[0] and rec[1]:
    #             exist_crm = self.env['crm.lead'].browse(rec[0])
    #             if rec[0] and exist_crm.exists():
    #                 exist_crm.with_context(th_test_import=True).write(rec[1])
    #                 if not exist_crm.partner_id.th_partner_aff_id and rec[3]:
    #                     exist_crm.partner_id.with_context(th_test_import=True).write(rec[3])
    #     return data
