from odoo.tests.common import TransactionCase
from odoo import fields
from odoo.tests import tagged
from markupsafe import Markup


@tagged('th_crm', 'create_lead_aff')
class TestCreateLeadAff(TransactionCase):
    def setUp(self):
        self.CrmLead = self.env['crm.lead']

    def test_create_lead_aff_new_partner(self):
        """Kiểm tra tạo cơ hội từ form nhúng khi chưa có liên hệ"""
        vals = {
            'th_customer': 'Test 1',
            'th_phone': '09988898991',
            'th_warehouse_code': 'AUM'
        }
        self.CrmLead.with_context(
            th_ownership_code='AUM',
            th_form_id='4551d615-a921-4176-ad98-3aa70dd6ee22',
            aff_crm_lead_form=True,
        ).create_lead_aff(vals)
        result = self.env['crm.lead'].search([('phone', '=', '09988898991')], limit=1)
        # # <PERSON><PERSON><PERSON> tra kết quả
        self.assertTrue(result.id, "<PERSON><PERSON> hội chưa được tạo")

    def test_create_lead_aff_created_partner(self):
        """Kiểm tra tạo cơ hội từ form khi đã có liên hệ và chưa có cơ hội CRM"""

        self.Partner = self.env['res.partner'].create({
            'name': 'Test 2',
            'phone': '09988898992'
        })

        vals = {
            'th_customer': 'Test 2',
            'th_phone': '09988898992',
            'th_warehouse_code': 'AUM'
        }

        self.CrmLead.with_context(
            th_ownership_code='AUM',
            th_form_id='4551d615-a921-4176-ad98-3aa70dd6ee22',
            aff_crm_lead_form=True,
        ).create_lead_aff(vals)
        result = self.env['crm.lead'].search([('phone', '=', '09988898992')], limit=1)
        # # Kiểm tra kết quả
        self.assertTrue(result.id, "Cơ hội chưa được tạo")

    def test_create_lead_aff_duplicate_lose(self):
        """Kiểm tra tạo cơ hội từ form khi đã có liên hệ và đã có cơ hội CRM
           Cơ hội vừa Check trùng thua """

        vals_1 = {
            'th_customer': 'Test 3',
            'th_phone': '09988898993',
            'th_warehouse_code': 'AUM'
        }

        vals_2 = {
            'th_customer': 'Test 3',
            'th_phone': '09988898993',
            'th_warehouse_code': 'AUM'
        }

        self.CrmLead.with_context(
            th_ownership_code='AUM',
            th_form_id='4551d615-a921-4176-ad98-3aa70dd6ee22',
            aff_crm_lead_form=True,
            ).create_lead_aff(vals_1)



        self.CrmLead.with_context(
            th_ownership_code='AUM',
            th_form_id='4551d615-a921-4176-ad98-3aa70dd6ee22',
            aff_crm_lead_form=True,
        ).create_lead_aff(vals_2)


        result = self.env['crm.lead'].search([('phone', '=', '09988898993')], limit=1)
        # # Kiểm tra kết quả
        self.assertTrue(result.th_is_a_duplicate_opportunity, "Cơ hội này không phải cơ hội trùng")

    def test_create_lead_aff_duplicate_won(self):
        """Kiểm tra tạo cơ hội từ form khi đã có liên hệ và đã có cơ hội CRM
           Cơ hội vừa Check trùng Thắng"""

        vals_1 = {
            'th_customer': 'Test 4',
            'th_phone': '09988898994',
            'th_warehouse_code': 'AUM'
        }

        vals_2 = {
            'th_customer': 'Test 4',
            'th_phone': '09988898994',
            'th_warehouse_code': 'AUM'
        }

        self.CrmLead.with_context(
            th_ownership_code='AUM',
            th_form_id='4551d615-a921-4176-ad98-3aa70dd6ee22',
            aff_crm_lead_form=True,
        ).create_lead_aff(vals_1)
        result_old = self.env['crm.lead'].search([('phone', '=', '09988898994')], limit=1)
        result_old.write({'stage_id': 6,
                    'th_status_group_id': 47,
                    'th_status_detail_id': 257,})

        self.CrmLead.with_context(
            th_ownership_code='AUM',
            th_form_id='4551d615-a921-4176-ad98-3aa70dd6ee22',
            aff_crm_lead_form=True,
        ).create_lead_aff(vals_2)

        result = self.env['crm.lead'].search([('phone', '=', '09988898994')], limit=1)
        # # Kiểm tra kết quả
        self.assertTrue(not result.th_is_a_duplicate_opportunity, "Cơ hội này không phải cơ hội trùng")