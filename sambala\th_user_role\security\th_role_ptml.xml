<?xml version="1.0" encoding="UTF-8"?>
<odoo>

<!--    <record id="th_category_role_ptml_root" model="ir.module.category">-->
<!--        <field name="name"><PERSON><PERSON><PERSON> triển mạng lưới</field>-->
<!--        <field name="description"><PERSON>hóm quyền của phát triển mạng lưới</field>-->
<!--        <field name="sequence">1</field>-->
<!--    </record>-->

<!--    <record id="th_category_role_ptml" model="ir.module.category">-->
<!--        <field name="name">PTML</field>-->
<!--        <field name="description">Nhóm quyền của phát triển mạng lưới</field>-->
<!--        <field name="parent_id" ref="th_category_role_ptml_root"/>-->
<!--        <field name="sequence">10</field>-->
<!--    </record>-->

    <record id="th_role_ptml_user" model="res.groups">
        <field name="name">Nhân viên PTML</field>
<!--        <field name="category_id" ref="th_category_role_aum_root"/>-->
        <field name="th_group_type">aum</field>
        <field name="implied_ids" eval="[
            (4, ref('th_template_user_base')),
            (4, ref('th_crm.th_group_user_crm')),
            (4, ref('th_apm.group_apm_user')),
            (4, ref('th_srm.group_srm_user')),
            (4, ref('th_prm.group_prm_user')),
            (4, ref('th_crm.th_group_crm_tvts')),
            (4, ref('th_srm_b2b.th_srm_b2b_partner_group')),
            (4, ref('th_apm_b2b.th_apm_group_partner_manager')),
            (4, ref('th_crm_b2b.th_crm_b2b_partner_group')),
            (4, ref('th_maintenance.group_maintenance_normal_user'))
        ]"/>
    </record>

    <record id="th_role_ptml_leader" model="res.groups">
        <field name="name">Trưởng nhóm PTML</field>
<!--        <field name="category_id" ref="th_category_role_aum_root"/>-->
        <field name="th_group_type">aum</field>
        <field name="implied_ids" eval="[
            (4, ref('th_role_ptml_user')),
            (4, ref('th_prm.group_prm_leader')),
            (4, ref('hr_attendance.group_hr_attendance_user'))
        ]"/>
    </record>
    <record id="th_role_ptml_head_leader" model="res.groups">
        <field name="name">Trưởng phòng PTML</field>
<!--        <field name="category_id" ref="th_category_role_aum_root"/>-->
        <field name="th_group_type">aum</field>
        <field name="implied_ids" eval="[
            (4, ref('th_role_ptml_leader')),
            (4, ref('th_prm.group_prm_manager')),
            (4, ref('hr_attendance.group_hr_attendance_user'))
        ]"/>
    </record>

<!--    Chú thích các nhóm quyền-->
<!--    th_group_user_crm: CRM:Nhân viên-->
<!--    group_apm_user: quyền APM:Nhân viên-->
<!--    group_srm_user: quyền SRM:Nhân viên-->
<!--    group_prm_user: quyền PRM:Nhân viên-->
<!--    th_group_crm_tvts: CRM bổ sung: TVTS - Xét tuyển-->
<!--    th_srm_b2b_partner_group: SRM Đối tác: Đối tác-->
<!--    th_apm_group_partner_manager: APM Đối tác: đối tác-->
<!--    th_crm_b2b_partner_group: CRM đối tác: đối tác-->
<!--    group_hr_attendance_user: Chấm công:Cán bộ-->



</odoo>
