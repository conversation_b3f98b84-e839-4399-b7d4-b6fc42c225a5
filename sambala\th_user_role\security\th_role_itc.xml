<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>
<!--        <record id="th_category_role_itc_root" model="ir.module.category">-->
<!--            <field name="name">Trung tâm IT</field>-->
<!--            <field name="description"><PERSON><PERSON> quyền quản lý Trung tâm IT</field>-->
<!--            <field name="sequence">1</field>-->
<!--        </record>-->

<!--        <record id="th_category_role_itc" model="ir.module.category">-->
<!--            <field name="name">IT</field>-->
<!--            <field name="description">Phân quyền quản lý Trung tâm IT</field>-->
<!--            <field name="parent_id" ref="th_category_role_itc_root"/>-->
<!--            <field name="sequence">15</field>-->
<!--        </record>-->

        <!-- <PERSON>ai trò: <PERSON><PERSON><PERSON><PERSON> viên ITC -->
        <record id="th_role_itc_dev_user" model="res.groups">
            <field name="name">Nhân viên IT(Dev)</field>
<!--            <field name="category_id" ref="th_category_role_aum_root"/>-->
            <field name="th_group_type">aum</field>
            <field name="implied_ids" eval="[
                (4, ref('th_template_user_base')),
                (4, ref('th_project_itc.th_group_project_user')),
                (4, ref('th_maintenance.group_maintenance_normal_user'))
            ]"/>
        </record>

        <record id="th_role_itc_qa_user" model="res.groups">
            <field name="name">Nhân viên IT(QA)</field>
<!--            <field name="category_id" ref="th_category_role_aum_root"/>-->
            <field name="th_group_type">aum</field>
            <field name="implied_ids" eval="[
                (4, ref('th_role_itc_dev_user')),
                (4, ref('th_project_itc.th_group_project_testcase_manager')),
                (4, ref('th_maintenance.group_maintenance_normal_user'))
            ]"/>
        </record>

        <record id="th_role_itc_qc_user" model="res.groups">
            <field name="name">Nhân viên IT(QC)</field>
<!--            <field name="category_id" ref="th_category_role_aum_root"/>-->
            <field name="th_group_type">aum</field>
            <field name="implied_ids" eval="[
                (4, ref('th_role_itc_dev_user')),
                (4, ref('helpdesk.group_helpdesk_user')),
                (4, ref('approvals.group_approval_user')),
                (4, ref('th_maintenance.group_maintenance_normal_user'))
            ]"/>
        </record>
        <record id="th_role_itc_its_user" model="res.groups">
            <field name="name">Nhân viên ITS</field>
<!--            <field name="category_id" ref="th_category_role_aum_root"/>-->
            <field name="th_group_type">aum</field>
            <field name="implied_ids" eval="[
                (4, ref('th_role_itc_dev_user')),
                (4, ref('helpdesk.group_helpdesk_user')),
                (4, ref('approvals.group_approval_user')),
                (4, ref('th_maintenance.group_equipment_leader')),
            ]"/>
        </record>

        <record id="th_role_itc_leader" model="res.groups">
            <field name="name">Trưởng nhóm IT</field>
<!--            <field name="category_id" ref="th_category_role_aum_root"/>-->
            <field name="th_group_type">aum</field>
            <field name="implied_ids" eval="[
                (4, ref('th_role_itc_qa_user')),
                (4, ref('th_project_itc.th_group_project_manager')),
                (4, ref('th_approvals.th_group_approval_manager_user')),
                (4, ref('hr_attendance.group_hr_attendance_user')),
                (4, ref('th_maintenance.group_maintenance_normal_user'))
            ]"/>
        </record>
        <record id="th_role_itc_leader_its" model="res.groups">
            <field name="name">Trưởng nhóm ITS</field>
<!--            <field name="category_id" ref="th_category_role_aum_root"/>-->
            <field name="th_group_type">aum</field>
            <field name="implied_ids" eval="[
                (4, ref('th_role_itc_dev_user')),
                (4, ref('th_helpdesk.group_helpdesk_care_manager')),
                (4, ref('th_approvals.th_group_approval_manager_user')),
                (4, ref('hr_attendance.group_hr_attendance_user')),
                (4, ref('maintenance.group_equipment_manager')),
            ]"/>
        </record>
<!--    Chú thích các nhóm quyền-->
<!--    th_group_project_user: Project ITC: Người dùng-->
<!--    -->
<!--    th_group_project_testcase_manager: Quản lý testcase-->
<!--        -->
<!--    group_hr_attendance_user: CHấm công:Cán bộ-->


    </data>
</odoo>