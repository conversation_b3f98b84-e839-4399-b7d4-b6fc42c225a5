<odoo>
    <record id="apm_lead_view_form" model="ir.ui.view">
        <field name="name">apm_lead_view_form</field>
        <field name="model">th.apm</field>
        <field name="arch" type="xml">
            <form string="">
                <header>
                    <field name="create_date" invisible="1"/>
                    <field name="th_last_status_stage" invisible="1"/>
                    <field name="th_sale_order_count" invisible="1"/>
                    <field name="th_order_created" invisible="1"/>
                    <field name="th_can_create_order" invisible="1" />
                    <button name="action_create_order" string="Tạo đơn hàng" type="object" class="oe_highlight"
                            attrs="{'invisible': ['|',('th_after_sales_care','!=',False),'|',('th_can_create_order', '!=', True), ('th_reason', '!=', False), ('th_after_sales_care','!=',True)]}"/>
                    <field name="th_stage_id" widget="th_statusbar" class="o_field_statusbar"
                           options="{'clickable':'1'}"
                           attrs="{'invisible': [('th_reason', '!=', False)]}" domain="th_stage_id_domain"/>
                    <field name="th_stage_id" widget="th_statusbar" class="o_field_statusbar"
                           attrs="{'invisible': [('th_reason', '=', False)]}" domain="th_stage_id_domain"/>
                    <button name="action_stop_caring" string="Dừng chăm sóc" type="object" class="oe_highlight"
                            attrs="{'invisible': ['|','|','|', ('th_last_status_stage', '=', True), ('th_reason', '!=', False),('th_is_lead_TTVH', '=', True), ('create_date', '=', False)]}"/>
                    <button name="compute_order_history" string="Cập nhật lịch sử mua hàng" type="object"
                            class="oe_highlight" attrs="{'invisible': [('th_is_lead_TTVH', '=', False)]}"/>
                </header>
                <sheet>
                    <field name="th_readonly_domain" invisible="0"/>
                    <div name="button_box" position="inside">
                        <button class="oe_stat_button" string="Đơn hàng" type="object" name="action_view_sale_order"
                                invisible="context.get('create')"
                                icon="fa-pencil-square-o"
                                attrs="{'invisible': ['|', ('th_sale_order_count', '!=', True),('th_reason', '!=', False),  ('th_order_created', '!=', True)]}">
                            <div class="o_stat_info">
                                <field name="sale_order_count" readonly="1" class="o_stat_value"/>
                                <span class="">
                                    Đơn hàng
                                </span>
                            </div>
                        </button>
                        <button name="action_open_apm_partner" type="object" class="oe_stat_button"
                                icon="fa-address-card"
                                attrs="{'invisible' :[('th_partner_id', '=', False)]}">
                            <div class="o_stat_info">
                                <span class="o_stat_text">
                                    Khách hàng
                                </span>
                            </div>
                        </button>
                    </div>
                    <widget name="web_ribbon" title="Dừng chăm sóc" bg_color="bg-danger"
                            attrs="{'invisible': [('th_reason', '=', False)]}"/>
                    <div name="button_box" position="inside" invisible="1">
                        <button class="oe_stat_button" type="object" name="action_view_sale_order" icon="fa-usd">
                            <field string="Sales" name="sale_order_count" widget="statinfo"/>
                        </button>
                    </div>
                    <div class="oe_title">
                        <h1>
                            <field class="text-break" name="name" placeholder="Tên cơ hội" readonly="1"/>
                        </h1>
                    </div>
                    <h4 class="d-flex gap-2 g-0 pb-3">
                        <div style="width: 15%; min-width: 4rem;">
                            <label for="th_last_check"/>
                            <br/>
                            <field name="th_last_check" widget="remaining_days" readonly="1"/>
                        </div>
                    </h4>
                    <group>
                        <group>
                            <field name="th_is_a_duplicate_opportunity" invisible="1"/>
                            <field name="th_customer_code" readonly="1"/>
                            <field name="th_customer_code_aum" invisible="1"/>
                            <field name="th_partner_id"
                                   options='{"no_open": True, "no_quick_create": True}' required="1"
                                   attrs="{'readonly': [('id', '!=', False)]}"
                                   context="{'default_phone': th_check_phone, 'default_email': th_check_email, 'default_th_partner_email': th_check_email, 'apm_contact': True}"
                                   string="Khách hàng*"/>
                            <field name="name_id_sequence" invisible="1"/>
                            <!--                            <field name="th_source_id"/>-->
                            <!--                            <field name="th_campaign_domain"/>-->
                            <field name="th_after_sales_care" invisible="1"/>
                            <field name="th_auto_update_products" invisible="1"/>
                            <field name="th_stage_id_domain" invisible="1"/>
                            <field name="th_marker_origin" invisible="1"/>
                            <field name="th_partner_phone" attrs="{'invisible': [('th_partner_id', '=', False)]}"/>
                            <field name="th_partner_email" string="Email" attrs="{'invisible': [('th_partner_id', '=', False)]}"/>
                            <field name="th_check_phone" attrs="{'readonly': [('th_ecommerce_platform', '=', True)],'invisible': [('th_partner_id', '!=', False)]}"/>
                            <field name="th_check_email" string="Email" attrs="{'readonly': [('th_ecommerce_platform', '=', True),('id', '=', False)],'invisible': [('th_partner_id', '!=', False)]}"/>
                            <field name="th_ownership_unit_id" options="{'no_create': True, 'no_open':True}"
                                   attrs="{'readonly': [('th_reason', '!=', False)]}" required="1"
                                   string="Đơn vị sở hữu*"/>
                            <field name="th_partner_reference_id" options="{'no_create': True, 'no_open':True}"
                                   attrs="{'readonly': ['|',('th_reason', '!=', False),('th_readonly_partner_reference', '=', True)]}"/>
                            <field name="th_readonly_partner_reference" invisible="1"/>
                            <field name="th_check_partner_referred" invisible="1"/>
                            <field name="th_status_category_id" options="{'no_create': True, 'no_open':True}"
                                   string="Nhóm trạng thái*" attrs="{'readonly': [('th_reason', '!=', False)]}" domain="th_status_category_domain"/>
                            <field name="th_status_detail_id" options="{'no_create': True, 'no_open':True}"
                                   domain="th_state_detail_domain"
                                   attrs="{'invisible': [('th_status_category_id', '=', False),('th_reason', '!=', False)],'readonly': [('th_reason', '!=', False)]}"
                                   string="Trạng thái chi tiết*"/>
                             <field name="th_processing_status_id" attrs="{'readonly': [('th_reason', '!=', False)], 'invisible': ['|',('th_status_detail_id', '=', False),('th_is_lead_TTVH', '!=', True)]}" options="{'no_create': True, 'no_open':True}"/>
                            <field name="th_state_detail_domain" invisible="1"/>
                            <field name="th_status_category_domain" invisible="1"/>
                            <field name="th_level_up_date"/>

                            <field name="th_estimated_level_up_date"
                                   attrs="{'invisible': ['|',('th_marker_origin', '!=', 'vstep'), ('th_after_sales_care', '=', True)]}"
                                   string="Ngày dự kiến lên L8"/>

                        </group>
                        <group>
                            <field name="th_check_group_admin_domain" invisible="1"/>
                            <field name="th_campaign_id" options="{'no_create': True, 'no_open':True}"
                                   attrs="{'readonly': ['|', ('th_reason', '!=', False),('th_check_group_admin_domain', '!=', True)]}"
                                   required="1" string="Chiến dịch*"/>
                            <field name="th_origin_id" options="{'no_create': True, 'no_open':True}" readonly="1"
                                   force_save="1" string="Dòng sản phẩm"
                                   />
                            <field name="th_origin_ids" domain="[('th_module_ids.name', 'in', ['APM'])]" options="{'no_create': True, 'no_open':True}"
                                   widget="many2many_tags" force_save="1" string="Dòng sản phẩm*"
                                   invisible="1"/>
                            <field name="th_product_category_id" domain="th_product_category_domain"
                                   options="{'no_create': True, 'no_open':True}"
                                   attrs="{'readonly': [('th_product_category_id', '!=', False)]}" force_save="1"
                                   invisible="1"/>
                            <field name="th_product_category_ids" string="Nhóm sản phẩm" options="{'no_create': True, 'no_open':True}" widget="many2many_tags"
                                   domain="th_product_categorys_domain"/>
                            <field name="th_product_categorys_domain" invisible="1"/>
                            <field name="th_product_ids" widget="many2many_tags"
                                   options="{'no_create': True, 'no_open':True}" domain="th_product_domain"
                                   attrs="{'readonly': [('th_reason', '!=', False)]}"/>
                            <!--                            <field name="th_need_id" options="{'no_quick_create': True}"/>-->
                           
                            <field name="th_course_type" attrs="{'invisible': ['|',('th_after_sales_care', '=', True), ('th_marker_origin', '!=', 'vstep')]}"/>
                            <field name="th_reason" options='{"no_open": True, "no_create": True}' readonly="1"
                                   attrs="{'invisible': [('th_reason', '=', False)]}"/>
                            <field name="th_user_id_domain" invisible="1"/>
                            <!-- <field name="th_apm_team_domain" invisible="1"/> -->
                            <!-- <field name="th_apm_team_id" options="{'no_create': True, 'no_open':True}"
                                   domain="th_apm_team_domain" attrs="{'readonly': [('th_reason', '!=', False)]}"/> -->
                            <field name="th_apm_dividing_ring_id" options="{'no_create': True, 'no_open':True}" attrs="{'readonly': [('th_reason', '!=', False)]}"/>
                            <field name="th_apm_dividing_ring_domain" invisible="1"/>
                            <field name="th_user_id" options="{'no_create': True, 'no_open':True}"
                                   domain="th_user_id_domain" attrs="{'readonly': [('th_reason', '!=', False)]}"/>
                            <field name="th_product_category_domain" invisible="1"/>
                            <field name="th_product_domain" invisible="1"/>
                            <field name="th_source_group_id"
                                   attrs="{'readonly': [('th_reason', '!=', False)], 'required': [('th_is_lead_TTVH', '=', False)]}"
                                   string="Nhóm nguồn*" options="{'no_open': True, 'no_create': True,'no_edit': True}"/>
                            <field name="th_source_name" attrs="{ 'invisible': [('th_after_sales_care', '=', True)]}"/>
                            <field name="th_channel_id"
                                   attrs="{'readonly': [('th_reason', '!=', False)]}" options="{'no_open': True, 'no_create': True,'no_edit': True}"/>
                            <field name="th_is_lead_TTVH" invisible="1"/>
                            <field name="th_campaign_domain" invisible="1"/>
                            <field name="th_resign_id" attrs="{'readonly': [('th_reason', '!=', False)], 'invisible': [('th_is_lead_TTVH', '!=', True)]}" options="{'no_create': True, 'no_open':True}"/>
                            <field name="th_detail_resign_id" attrs="{'readonly': [('th_reason', '!=', False)], 'invisible': ['|',('th_resign_id', '=', False),('th_is_lead_TTVH', '!=', True)]}" options="{'no_create': True, 'no_open':True}"/>
                            <field name="th_day_recent_purchase" readonly="True" attrs="{ 'invisible': [('th_is_lead_TTVH', '!=', True)]}"/>
                            <field name="th_first_day_purchase" string="Ngày mua lần 1" attrs="{ 'invisible': [('th_after_sales_care', '!=', True)]}"/>
                            <field name="th_ecommerce_platform" attrs="{'readonly': [('id', '!=', False)], 'invisible': [('th_after_sales_care', '=', True)]}"/>
                            <field name="create_date" readonly="True" string="Ngày tạo cơ hội"
                                   attrs="{ 'invisible': [('th_is_lead_TTVH', '!=', True)]}"/>
                        </group>
                        <group>
                            <field name="th_check_vstep_field" invisible="1"/>
                            <field name="th_student_code" attrs="{'invisible': [('th_check_vstep_field','=',False)]}"/>
                            <field name="th_shift_selection" attrs="{ 'invisible': [('th_check_vstep_field','=',False)]}"/>
                            <field name="th_test_date" attrs="{ 'invisible': [('th_check_vstep_field','=',False)]}"/>
                            <field name="th_test_result" attrs="{ 'invisible': [('th_check_vstep_field','=',False)]}"/>
                        </group>
                    </group>
                    <notebook>
                        <page string="Mô tả">
                            <field name="th_description" placeholder="Thêm mô tả..."/>
                        </page>
                        <page string="Đặc điểm">
                            <field name="th_apm_contact_trait_ids">
                                <tree editable="bottom" no_open="1">
                                    <field name="th_origin_id"  options="{'no_edit': True, 'no_create': True, 'no_open': True}"/>
                                    <field name="th_apm_trait_id" domain="th_trait_domain" options="{'no_edit': True, 'no_create': True, 'no_open': True}"/>
                                    <field name="th_apm_trait_value_ids" widget="many2many_tags"
                                           domain="th_trait_value_domain" options="{'no_edit': True, 'no_create': True, 'no_open': True}"/>
                                    <field name="th_trait_domain" invisible="1"/>
                                    <field name="th_trait_value_domain" invisible="1"/>

                                </tree>
                            </field>
                        </page>
                        <page string="Dữ liệu Getfly" attrs="{'invisible': [('th_data_getfly', '=', False)]}">
                            <field name="th_data_getfly" readonly="1"/>
                        </page>
                        <page string="Cơ hội">
                            <field name="th_opportunity_list_partner_ids">
                                <tree no_open="1">
                                    <field name="name"/>
                                    <field name="th_origin_id"/>
                                    <field name="th_last_check"/>
                                    <field name="th_campaign_id"/>
                                    <field name="th_stage_id"/>
                                    <field name="th_ownership_unit_id"/>
                                </tree>
                            </field>
                        </page>
                        <page string="Lịch sử mua hàng" attrs="{'invisible': [('th_is_lead_TTVH', '=', False)]}">
                            <field name="th_order_history_ids">
                                <tree create="0" edit="0" delete="0" no_open="1">
                                    <field name="th_sale_order_id"/>
                                    <field name="th_origin_id"/>
                                    <field name="th_product_ids" widget="many2many_tags"/>
                                    <field name="th_create_date"/>
                                    <field name="th_ownership_unit_id" invisible="1"/>
                                    <field name="th_ownership_unit_new_id"/>
                                    <field name="th_channel_id" />
                                    <field name="user_id"/>
                                    <field name="th_apm_source_group_id"/>
                                    <field name="th_introducer_id"/>

                                </tree>
                            </field>
                        </page>
                         <page string="Tình trạng học viên"  attrs="{'invisible': [('th_is_lead_TTVH', '=', False)]}">
                             <field name="th_check_c" invisible="1"/>
                               <button name="action_open_import_student_status" string="Import" type="object" class="btn-primary"/>
                               <button name="action_export_data" string="Export" type="object" class="btn-primary"/>
                            <field name="th_student_status_ids"  attrs="{'readonly': [('th_reason', '!=', False)]}">
                                <tree editable="bottom" create="0" delete="0" no_open="1" >
                                    <field name="th_origin_ids" widget="many2many_tags"/>
                                    <field name="th_sale_order_id" readonly="1" options="{'no_open': True}"/>
                                    <field name="th_ownership_unit_id" readonly="1" />
                                    <field name="th_product_id" readonly="1" options="{'no_open': True}"/>
                                    <field name="th_level" options='{"no_open": True, "no_create": True}'/>
                                    <field name="th_registration_date"/>
                                    <field name="th_activation_date" />
                                    <field name="th_closing_date" readonly="True"/>
                                    <field name="th_renewal_date"/>
                                    <field name="th_extension_end_date" readonly="True"/>
                                </tree>
                            </field>
                        </page>
                        <page string="Thông tin giới thiệu" attrs="{'invisible': [('th_utm_source', '=', False),('th_utm_medium', '=', False),('th_utm_campaign', '=', False),('th_utm_term', '=', False),('th_utm_content', '=', False)]}">
                            <group>
                                <field name="th_utm_source" readonly="1"/>
                                <field name="th_utm_medium" readonly="1"/>
                                <field name="th_utm_campaign" readonly="1"/>
                                <field name="th_utm_term" readonly="1"/>
                                <field name="th_utm_content" readonly="1"/>
                            </group>
                        </page>
                    </notebook>
                </sheet>
                <div class="oe_chatter">
                    <field name="message_follower_ids"/>
                    <field name="activity_ids"/>
                    <field name="message_ids"/>
                </div>
            </form>
        </field>
    </record>

    <record id="apm_lead_view_tree" model="ir.ui.view">
        <field name="name">apm_lead_view_tree</field>
        <field name="model">th.apm</field>
        <field name="arch" type="xml">
            <tree string="" default_order="th_last_check desc">
                <field name="th_customer_code" widget="tree_url" optional="show" />
                <field name="th_customer_code_aum" widget="tree_url" optional="show" invisible="1"/>
                <field name="th_last_check" widget="remaining_days" optional="show" readonly="1"/>
                <field name="th_partner_id" optional="show"/>
                <field name="th_partner_phone" optional="show"/>
                <field name="th_partner_email" string="Email" optional="show"/>
                <field name="th_origin_ids" widget="many2many_tags" optional="show" invisible="1"/>
                <field name="th_origin_id" optional="show" />
                <field name="th_product_ids" widget="many2many_tags" optional="show"/>
                <field name="th_course_type" optional="hide" invisible="context.get('is_after_order', False)"/>
                <field name="th_product_category_ids" string="Nhóm sản phẩm" widget="many2many_tags" optional="show"/>
                <field name="th_user_id" optional="show"/>
                <field name="th_status_category_id" optional="show"/>
                <field name="th_status_detail_id" optional="show"/>
                <field name="th_processing_status_id" optional="show"/>
                <field name="th_stage_id" optional="show"/>
                <field name="th_partner_reference_id" optional="show"/>
                <field name="th_ownership_unit_id" optional="hide"/>
                <field name="th_campaign_id" optional="hide"/>
                <field name="th_status_payment" optional="hide"/>
                <field name="th_detail_resign_id" optional="hide"/>
                <field name="th_day_recent_purchase" optional="hide"/>
                <field name="create_date" optional="hide"/>
                <field name="create_uid" string="Người tạo cơ hội" optional="hide"/>
                <field name="th_first_day_purchase" optional="hide"/>
                <field name="th_source_name" optional="show" invisible="context.get('is_after_order', False)"/>
                <field name="th_estimated_level_up_date" invisible="context.get('is_after_order', False)" string="Ngày dự kiến lên L8" optional="show"/>
                <field name="th_description" optional="show"/>
            </tree>
        </field>
    </record>

    <record id="apm_stop_caring_form" model="ir.ui.view">
        <field name="name">apm_stop_caring_form</field>
        <field name="model">th.apm</field>
        <field name="arch" type="xml">
            <form string="Lý do">
                <group>
                    <field name="th_reason"/>
                </group>
                <footer>
                    <button name="action_stop_caring_save" type="object" string="Lưu" class="btn btn-primary"/>
                    <button string="Hủy" special="cancel" class="btn btn-secondary"/>
                </footer>
            </form>
        </field>
    </record>
<!--search view cho cơ hội -->
    <record id="apm_lead_view_search" model="ir.ui.view">
        <field name="name">th.apm.lead.search</field>
        <field name="model">th.apm</field>
        <field name="arch" type="xml">
            <search string="">
                <field name="name"/>
                <field name="th_customer_code" />
                <field name="th_customer_code_aum" invisible="1"/>
                <field name="th_partner_id" filter_domain="[('th_partner_id.name', 'ilike', self)]"/>
                <field name="create_uid" string="Người tạo cơ hội"/>
                <field name="th_partner_phone"/>
                <field name="th_partner_email" string="Email"/>
                <field name="th_user_id"/>
                <field name="th_course_type" />
                <field name="th_product_ids" invisible="1"/>
                <field name="th_product_ids" filter_domain="['|', ('th_product_ids.name', 'ilike', self), ('th_product_ids.default_code', 'ilike', self)]"/>
                <!--                <field name="th_stage_id"/>-->
                <field name="th_source_name" />
                <field name="th_partner_reference_id"/>
                <field name="th_origin_id"/>
                <field name="th_estimated_level_up_date" string="Ngày dự kiến lên L8"/>
                <filter name="filter_stop_caring" string="Dừng chăm sóc" domain="[('th_reason', '!=', False)]"/>
                <filter name="user_id" string="Chưa có người chăm sóc" domain="[('th_user_id', '=', False)]"/>
                <filter name="filter_ordered" string="Đã có đơn hàng" domain="[('th_order_id', '!=', False)]"/>
                <filter name="filter_ordered" string="Đang chăm sóc"
                        domain="[('th_order_id', '=', False),('th_reason', '=', False)]"/>
                <separator/>
                <filter name="filter_ordered" string="Cơ hội của chiến dịch đã hoàn thành"
                        domain="[('active', '=', False)]"/>
                <searchpanel>
                    <field name="th_stage_id" icon="fa-user-plus" enable_counters="1"/>
                    <field name="th_status_category_id" icon="fa-phone" enable_counters="1"/>
                    <field name="th_status_detail_id" icon="fa-phone" enable_counters="1"/>
                    <field name="th_user_id" icon="fa-user-plus" enable_counters="1"/>
                </searchpanel>
                <filter name="lead_today" string="Cơ hội hôm nay"
                        domain="[('create_date', '&gt;', (context_today() - datetime.timedelta(days=1)).strftime('%Y-%m-%d'))]"/>
                <group>
                    <filter name="th_product_category_ids" string="Nhóm sản phẩm"
                            context="{'group_by': 'th_product_category_ids'}"/>
                    <filter name="th_course_type" string="Khóa học" context="{'group_by':'th_course_type'}"/>
                    <filter name="th_source_group_id" string="Nhóm nguồn" context="{'group_by':'th_source_group_id'}"/>
                    <filter name="th_source_name" string="Tên nguồn" context="{'group_by':'th_source_name'}"/>
                    <filter name="th_channel_id" string="Kênh" context="{'group_by':'th_channel_id'}"/>
                    <filter name="create_uid" string="Người tạo cơ hội" context="{'group_by':'create_uid'}"/>
                    <filter name="th_ownership_unit_id" string="Đơn vị sở hữu" context="{'group_by':'th_ownership_unit_id'}"/>
                    <filter name="stage_id" string="Mối quan hệ" context="{'group_by':'th_stage_id'}"/>
                    <filter name="th_campaign_id" string="Chiến dịch" context="{'group_by':'th_campaign_id'}"/>
                    <filter name="last_check" string="Liên hệ lần cuối" context="{'group_by':'th_last_check:day'}"/>
                    <filter name="last_check_1" string="Liên hệ cuối từ 1 ngày trở lên"
                            domain="[('th_last_check', '&lt;', (context_today() - datetime.timedelta(days=0)))]"/>
                    <filter name="last_check_5" string="Liên hệ cuối từ 4 ngày trở lên"
                            domain="[('th_last_check', '&lt;', (context_today() - datetime.timedelta(days=3)))]"/>
                    <filter name="last_check_15" string="Liên hệ cuối từ 15 ngày trở lên"
                            domain="[('th_last_check', '&lt;', (context_today() - datetime.timedelta(days=14)))]"/>
                    <filter name="last_check_30" string="Liên hệ cuối từ 30 ngày trở lên"
                            domain="[('th_last_check', '&lt;', (context_today() - datetime.timedelta(days=29)))]"/>
                    <filter name="last_check_0_to_45" string="Liên hệ cuối từ 0 đến 45 ngày"
                            domain="[('th_last_check', '&lt;', (context_today() - datetime.timedelta(days=-1))),
                                     ('th_last_check', '&gt;', (context_today() - datetime.timedelta(days=46)))]"/>
                </group>
            </search>
        </field>
    </record>
<!--searchview cho cơ hội sau bán-->
    <record id="apm_lead_after_sale_view_search" model="ir.ui.view">
        <field name="name">th.apm.lead.after.sale.search</field>
        <field name="model">th.apm</field>
        <field name="arch" type="xml">
            <search string="">
                <field name="name"/>
                <field name="th_customer_code" />
                <field name="th_customer_code_aum" invisible="1"/>
                <field name="th_partner_id" filter_domain="[('th_partner_id.name', 'ilike', self)]"/>
                <field name="th_partner_phone"/>
                <field name="th_partner_email" string="Email"/>
                <field name="th_user_id"/>
                <field name="th_product_ids" invisible="1"/>
                <field name="th_product_ids" filter_domain="['|', ('th_product_ids.name', 'ilike', self), ('th_product_ids.default_code', 'ilike', self)]"/>
<!--                <field name="th_stage_id"/>-->
                <field name="th_partner_reference_id"/>
                <field name="th_origin_ids"/>
                <filter name="th_product_category_ids" string="Nhóm sản phẩm" context="{'group_by': 'th_product_category_ids'}"/>
                <filter name="th_day_recent_purchase_filter" string="Ngày mua gần nhất" context="{'group_by': 'th_day_recent_purchase_filter'}"/>
                <filter name="create_date" string="Ngày tạo cơ hội" context="{'group_by': 'create_date'}"/>
                <filter name="filter_stop_caring" string="Dừng chăm sóc" domain="[('th_reason', '!=', False)]"/>
                <filter name="user_id" string="Chưa có người chăm sóc" domain="[('th_user_id', '=', False)]"/>
                <filter name="filter_ordered" string="Đã có đơn hàng" domain="[('th_order_id', '!=', False)]"/>
                <filter name="filter_ordered" string="Đang chăm sóc"
                        domain="[('th_order_id', '=', False),('th_reason', '=', False)]"/>
                <separator/>
                <filter name="filter_ordered" string="Cơ hội của chiến dịch đã hoàn thành"
                        domain="[('active', '=', False)]"/>
                <searchpanel>
                    <field name="th_stage_id" icon="fa-user-plus" enable_counters="1"/>
                    <field name="th_status_category_id" icon="fa-phone" enable_counters="1"/>
                    <field name="th_status_detail_id" icon="fa-phone" enable_counters="1"/>
                    <field name="th_user_id" icon="fa-user-plus" enable_counters="1"/>
                    <field name="th_detail_resign_id" icon="fa-user-plus" enable_counters="1" />
                    <field name="th_processing_status_id" icon="fa-user-plus" enable_counters="1" />
                </searchpanel>
                <filter name="lead_today" string="Cơ hội hôm nay"
                        domain="[('create_date', '&gt;', (context_today() - datetime.timedelta(days=1)).strftime('%Y-%m-%d'))]"/>
                <group>
                    <filter name="th_ownership_unit_id" string="Đơn vị sở hữu" context="{'group_by':'th_ownership_unit_id'}"/>
                    <filter name="stage_id" string="Mối quan hệ" context="{'group_by':'th_stage_id'}"/>
                    <filter name="th_campaign_id" string="Chiến dịch" context="{'group_by':'th_campaign_id'}"/>
                    <filter name="last_check" string="Liên hệ lần cuối" context="{'group_by':'th_last_check:day'}"/>
                    <filter name="last_check_1" string="Liên hệ cuối từ 1 ngày trở lên"
                            domain="[('th_last_check', '&lt;', (context_today() - datetime.timedelta(days=0)))]"/>
                    <filter name="last_check_5" string="Liên hệ cuối từ 4 ngày trở lên"
                            domain="[('th_last_check', '&lt;', (context_today() - datetime.timedelta(days=3)))]"/>
                    <filter name="last_check_15" string="Liên hệ cuối từ 15 ngày trở lên"
                            domain="[('th_last_check', '&lt;', (context_today() - datetime.timedelta(days=14)))]"/>
                    <filter name="last_check_30" string="Liên hệ cuối từ 30 ngày trở lên"
                            domain="[('th_last_check', '&lt;', (context_today() - datetime.timedelta(days=29)))]"/>
                </group>
            </search>
        </field>
    </record>

    <record id="apm_lead_view_act" model="ir.actions.act_window">
        <field name="name">Cơ hội</field>
        <field name="type">ir.actions.act_window</field>
        <field name="domain">[('th_after_sales_care', '!=', True),('th_is_a_duplicate_opportunity', '=', False)]</field>
        <field name="res_model">th.apm</field>
        <field name="view_mode">tree,form</field>
        <field name="search_view_id" ref="apm_lead_view_search"/>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                Chưa có dữ liệu
            </p>
        </field>
    </record>

    <record id="apm_lead_after_sale_view_act" model="ir.actions.act_window">
        <field name="name">Cơ hội sau bán</field>
        <field name="type">ir.actions.act_window</field>
        <field name="domain">[('th_after_sales_care', '=', True)]</field>
        <field name="res_model">th.apm</field>
        <field name="context">{'create':0, 'is_after_order': True}</field>
        <field name="view_mode">tree,form</field>
        <field name="search_view_id" ref="apm_lead_after_sale_view_search"/>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                Chưa có dữ liệu
            </p>
        </field>
    </record>

    <record id="name_sequence" model="ir.sequence">
        <field name="name">Field Incrementation</field>
        <field name="code">th.apm</field>
        <field name="prefix">M</field>
        <field name="padding">6</field>
        <field name="company_id" eval="False"/>
    </record>

    <odoo>
        <record id="th_action_data_merge_record_apm_notification" model="ir.actions.act_window">
            <field name="name">Trùng cơ hội</field>
            <field name="res_model">data_merge.record</field>
            <field name="domain">[('res_model_name','=','th.apm')]</field>
            <field name="view_id" ref="data_merge.view_data_merge_record_list"/>
            <field name="search_view_id" ref="data_merge.view_data_merge_record_search"/>
            <field name="context">{ 'group_by': ['group_id']}</field>
        </record>

        <record id="model_data_merge_record_action_duplicate_apm" model="ir.actions.server">
            <field name="name">Trùng cơ hội</field>
            <field name="model_id" ref="th_apm.model_th_apm"/>
            <field name="state">code</field>
            <field name="code">
                action = model.th_action_deduplicate_apm()
            </field>
        </record>
    </odoo>

    <!-- This Menu Item must have a parent and an action -->
    <!--    <menuitem id="th_product_template_menu" name="SP" parent="" action="th_product_template_action" sequence=""/>-->
</odoo>