<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- Form View cho Cơ hội sau bán -->
    <record id="th_apm_vstep_lead_after_sale_form_view" model="ir.ui.view">
        <field name="name">th.apm.vsttep.lead.after.sale.form</field>
        <field name="model">th.apm</field>
        <field name="arch" type="xml">
            <form string="">
                <header>
                    <field name="create_date" invisible="1"/>
                    <field name="th_last_status_stage" invisible="1"/>
                    <field name="th_sale_order_count" invisible="1"/>
                    <field name="th_order_created" invisible="1"/>
                    <field name="th_can_create_order" invisible="1" />
                    <button name="action_create_order" string="Tạo đơn hàng" type="object" class="oe_highlight"
                            attrs="{'invisible': ['|',('th_after_sales_care','!=',False),'|',('th_can_create_order', '!=', True), ('th_reason', '!=', False), ('th_after_sales_care','!=',True)]}"/>
                    <field name="th_stage_id" widget="th_statusbar" class="o_field_statusbar"
                           options="{'clickable':'1'}"
                           attrs="{'invisible': [('th_reason', '!=', False)]}" domain="th_stage_id_domain"/>
                    <field name="th_stage_id" widget="th_statusbar" class="o_field_statusbar"
                           attrs="{'invisible': [('th_reason', '=', False)]}" domain="th_stage_id_domain"/>
                    <button name="action_stop_caring" string="Dừng chăm sóc" type="object" class="oe_highlight"
                            attrs="{'invisible': ['|','|','|', ('th_last_status_stage', '=', True), ('th_reason', '!=', False),('th_is_lead_TTVH', '=', True), ('create_date', '=', False)]}"/>
                    <button name="compute_order_history" string="Cập nhật lịch sử mua hàng" type="object"
                            class="oe_highlight" attrs="{'invisible': [('th_is_lead_TTVH', '=', False)]}"/>
                </header>
                <sheet>
                    <div name="button_box" position="inside">
                        <button class="oe_stat_button" string="Đơn hàng" type="object" name="action_view_sale_order"
                                icon="fa-pencil-square-o"
                                attrs="{'invisible': ['|', ('th_sale_order_count', '!=', True), ('th_reason', '!=', False)]}">
                            <div class="o_stat_info">
                                <field name="sale_order_count" readonly="1" class="o_stat_value"/>
                                <span class="">Đơn hàng</span>
                            </div>
                        </button>
                        <button name="action_open_apm_partner" type="object" class="oe_stat_button"
                                icon="fa-address-card"
                                attrs="{'invisible' :[('th_partner_id', '=', False)]}">
                            <div class="o_stat_info">
                                <span class="o_stat_text">Khách hàng</span>
                            </div>
                        </button>
                    </div>
                    <widget name="web_ribbon" title="Dừng chăm sóc" bg_color="bg-danger"
                            attrs="{'invisible': [('th_reason', '=', False)]}"/>
                    <div name="button_box" position="inside" invisible="1">
                        <button class="oe_stat_button" type="object" name="action_view_sale_order" icon="fa-usd">
                            <field string="Sales" name="sale_order_count" widget="statinfo"/>
                        </button>
                    </div>
                    <div class="oe_title">
                        <h1>
                            <field class="text-break" name="name" placeholder="Tên cơ hội" readonly="1"/>
                        </h1>
                    </div>
                    <h4 class="d-flex gap-2 g-0 pb-3">
                        <div style="width: 15%; min-width: 4rem;">
                            <label for="th_last_check"/>
                            <br/>
                            <field name="th_last_check" widget="remaining_days" readonly="1"/>
                        </div>
                    </h4>
                    <group>
                        <group>
                            <field name="th_customer_code" readonly="1"/>
                            <field name="th_customer_code_aum" invisible="1"/>
                            <field name="th_partner_id"
                                   options='{"no_open": True, "no_quick_create": True}' required="1"
                                   attrs="{'readonly': [('id', '!=', False)]}"
                                   context="{'default_phone': th_check_phone, 'default_email': th_check_email, 'default_th_partner_email': th_check_email, 'apm_contact': True}"
                                   string="Khách hàng*"/>
                            <field name="name_id_sequence" invisible="1"/>
                            <field name="th_after_sales_care" invisible="1"/>
                            <field name="th_auto_update_products" invisible="1"/>
                            <field name="th_stage_id_domain" invisible="1"/>
                            <field name="th_marker_origin" invisible="1"/>
                            <field name="th_partner_phone" attrs="{'invisible': [('th_partner_id', '=', False)]}"/>
                            <field name="th_partner_email" string="Email" attrs="{'invisible': [('th_partner_id', '=', False)]}"/>
                            <field name="th_check_phone" attrs="{'readonly': [('th_ecommerce_platform', '=', True)],'invisible': [('th_partner_id', '!=', False)]}"/>
                            <field name="th_check_email" string="Email" attrs="{'readonly': [('th_ecommerce_platform', '=', True),('id', '=', False)],'invisible': [('th_partner_id', '!=', False)]}"/>
                            <field name="th_ownership_unit_id" options="{'no_create': True, 'no_open':True}"
                                   attrs="{'readonly': [('th_reason', '!=', False)]}" required="1"
                                   string="Đơn vị sở hữu*"/>
                            <field name="th_partner_reference_id" options="{'no_create': True, 'no_open':True}"
                                   attrs="{'readonly': ['|',('th_reason', '!=', False),('th_readonly_partner_reference', '=', True)]}"/>
                            <field name="th_readonly_partner_reference" invisible="1"/>
                            <field name="th_check_partner_referred" invisible="1"/>
                            <field name="th_status_category_id" options="{'no_create': True, 'no_open':True}"
                                   string="Nhóm trạng thái*" attrs="{'readonly': [('th_reason', '!=', False)]}" domain="th_status_category_domain"/>
                            <field name="th_status_detail_id" options="{'no_create': True, 'no_open':True}"
                                   domain="th_state_detail_domain"
                                   attrs="{'invisible': [('th_status_category_id', '=', False),('th_reason', '!=', False)],'readonly': [('th_reason', '!=', False)]}"
                                   string="Trạng thái chi tiết*"/>
                             <field name="th_processing_status_id" attrs="{'readonly': [('th_reason', '!=', False)], 'invisible': ['|',('th_status_detail_id', '=', False),('th_is_lead_TTVH', '!=', True)]}" options="{'no_create': True, 'no_open':True}"/>
                            <field name="th_state_detail_domain" invisible="1"/>
                            <field name="th_status_category_domain" invisible="1"/>
                            <field name="th_level_up_date"/></group>
                            <field name="th_estimated_level_up_date"
                                   attrs="{'invisible': ['|',('th_marker_origin', '!=', 'vstep'), ('th_after_sales_care', '=', True)]}"
                                   string="Ngày dự kiến lên L8"/>
                        <group>
                            <field name="th_check_group_admin_domain" invisible="1"/>
                            <field name="th_campaign_id" options="{'no_create': True, 'no_open':True}"
                                   attrs="{'readonly': ['|', ('th_reason', '!=', False),('th_check_group_admin_domain', '!=', True)]}"
                                   required="1" string="Chiến dịch*"/>
                            <field name="th_origin_id" options="{'no_create': True, 'no_open':True}" readonly="1"
                                   force_save="1" string="Dòng sản phẩm"/>
                            <field name="th_product_category_id" domain="th_product_category_domain"
                                   options="{'no_create': True, 'no_open':True}"
                                   attrs="{'readonly': [('th_product_category_id', '!=', False)]}" force_save="1"
                                   invisible="1"/>
                            <field name="th_product_category_ids" string="Nhóm sản phẩm" options="{'no_create': True, 'no_open':True}" widget="many2many_tags"
                                   domain="th_vstep_product_category_domain"/>
                            <field name="th_vstep_product_category_domain" invisible="1"/>
                            <field name="th_product_ids" widget="many2many_tags"
                                   options="{'no_create': True, 'no_open':True}" domain="th_product_domain"
                                   attrs="{'readonly': [('th_reason', '!=', False)]}"/>
                            <field name="th_course_type" attrs="{'invisible': ['|',('th_after_sales_care', '=', True), ('th_marker_origin', '!=', 'vstep')]}"/>
                            <field name="th_reason" options='{"no_open": True, "no_create": True}' readonly="1"
                                   attrs="{'invisible': [('th_reason', '=', False)]}"/>
                            <field name="th_user_id_domain" invisible="1"/>
                            <field name="th_apm_dividing_ring_id" options="{'no_create': True, 'no_open':True}" attrs="{'readonly': [('th_reason', '!=', False)]}"/>
                            <field name="th_apm_dividing_ring_domain" invisible="1"/>
                            <field name="th_user_id" options="{'no_create': True, 'no_open':True}"
                                   domain="th_user_id_domain" attrs="{'readonly': [('th_reason', '!=', False)]}"/>
                            <field name="th_product_category_domain" invisible="1"/>
                            <field name="th_product_domain" invisible="1"/>
                            <field name="th_source_group_id"
                                   attrs="{'readonly': [('th_reason', '!=', False)], 'required': [('th_is_lead_TTVH', '=', False)]}"
                                   string="Nhóm nguồn*" options="{'no_open': True, 'no_create': True,'no_edit': True}"/>
                            <field name="th_source_name" attrs="{ 'invisible': [('th_after_sales_care', '=', True)]}"/>
                            <field name="th_channel_id"
                                   attrs="{'readonly': [('th_reason', '!=', False)]}" options="{'no_open': True, 'no_create': True,'no_edit': True}"/>
                            <field name="th_is_lead_TTVH" invisible="1"/>
                            <field name="th_campaign_domain" invisible="1"/>
                            <field name="th_resign_id" attrs="{'readonly': [('th_reason', '!=', False)], 'invisible': [('th_is_lead_TTVH', '!=', True)]}" options="{'no_create': True, 'no_open':True}"/>
                            <field name="th_detail_resign_id" attrs="{'readonly': [('th_reason', '!=', False)], 'invisible': ['|',('th_resign_id', '=', False),('th_is_lead_TTVH', '!=', True)]}" options="{'no_create': True, 'no_open':True}"/>
                            <field name="th_day_recent_purchase" readonly="True" attrs="{ 'invisible': [('th_is_lead_TTVH', '!=', True)]}"/>
                            <field name="th_first_day_purchase" string="Ngày mua lần 1" attrs="{ 'invisible': [('th_after_sales_care', '!=', True)]}"/>
                            <field name="th_ecommerce_platform" attrs="{'readonly': [('id', '!=', False)], 'invisible': [('th_after_sales_care', '=', True)]}"/>
                            <field name="create_date" readonly="True" string="Ngày tạo cơ hội"
                                   attrs="{ 'invisible': [('th_is_lead_TTVH', '!=', True)]}"/>
                        </group>
                    </group>
                    <notebook>
                        <page string="Mô tả">
                            <field name="th_description" placeholder="Thêm mô tả..."/>
                        </page>
                        <page string="Đặc điểm">
                            <field name="th_apm_contact_trait_ids">
                                <tree editable="bottom" no_open="1">
                                    <field name="th_origin_id"  options="{'no_edit': True, 'no_create': True, 'no_open': True}" domain="th_origin_id_domain"/>
                                    <field name="th_origin_id_domain" invisible="1"/>
                                    <field name="th_apm_trait_id" domain="th_trait_domain" options="{'no_edit': True, 'no_create': True, 'no_open': True}"/>
                                    <field name="th_apm_trait_value_ids" widget="many2many_tags"
                                           domain="th_trait_value_domain" options="{'no_edit': True, 'no_create': True, 'no_open': True}"/>
                                    <field name="th_trait_domain" invisible="1"/>
                                    <field name="th_trait_value_domain" invisible="1"/>
                                </tree>
                            </field>
                        </page>
                        <page string="Cơ hội">
                            <field name="th_opportunity_list_partner_ids">
                                <tree no_open="1">
                                    <field name="name"/>
                                    <field name="th_origin_id"/>
                                    <field name="th_last_check"/>
                                    <field name="th_campaign_id"/>
                                    <field name="th_stage_id"/>
                                    <field name="th_ownership_unit_id"/>
                                </tree>
                            </field>
                        </page>
                        <page string="Lịch sử mua hàng">
                            <field name="th_order_history_ids">
                                <tree create="0" edit="0" delete="0" no_open="1">
                                    <field name="th_sale_order_id"/>
                                    <field name="th_origin_id"/>
                                    <field name="th_product_ids" widget="many2many_tags"/>
                                    <field name="th_create_date"/>
                                    <field name="th_ownership_unit_id" invisible="1"/>
                                    <field name="th_ownership_unit_new_id"/>
                                    <field name="th_channel_id" />
                                    <field name="user_id"/>
                                    <field name="th_apm_source_group_id"/>
                                    <field name="th_introducer_id"/>
                                </tree>
                            </field>
                        </page>
                        <page string="Tình trạng học viên">
                            <field name="th_check_c" invisible="1"/>
                               <button name="action_open_import_student_status" string="Import" type="object" class="btn-primary"/>
                               <button name="action_export_data" string="Export" type="object" class="btn-primary"/>
                            <field name="th_student_status_ids"  attrs="{'readonly': [('th_reason', '!=', False)]}" invisible="">
                                <tree editable="bottom" create="0" delete="0" no_open="1" >
                                    <field name="th_origin_id"/>
                                    <field name="th_sale_order_id" readonly="1" options="{'no_open': True}"/>
                                    <field name="th_ownership_unit_id" readonly="1" />
                                    <field name="th_product_id" readonly="1" options="{'no_open': True}"/>
                                    <field name="th_level" options='{"no_open": True, "no_create": True}'/>
                                    <field name="th_registration_date"/>
                                    <field name="th_activation_date" />
                                    <field name="th_closing_date" readonly="True"/>
                                    <field name="th_renewal_date"/>
                                    <field name="th_extension_end_date" readonly="True"/>
                                </tree>
                            </field>
                        </page>
                    </notebook>
                </sheet>
                <div class="oe_chatter">
                    <field name="message_follower_ids"/>
                    <field name="activity_ids"/>
                    <field name="message_ids"/>
                </div>
            </form>
        </field>
    </record>

    <!-- Tree View cho Cơ hội sau bán -->
    <record id="th_apm_vstep_lead_after_sale_tree_view" model="ir.ui.view">
        <field name="name">th.apm.vstep.lead.after.sale.tree</field>
        <field name="model">th.apm</field>
        <field name="arch" type="xml">
            <tree create="0" string="" default_order="th_last_check desc">
                <field name="th_customer_code" widget="tree_url" optional="show" />
                <field name="th_last_check" widget="remaining_days"/>
                <field name="th_partner_id" optional="show"/>
                <field name="th_partner_phone" optional="show"/>
                <field name="th_partner_email" string="Email" optional="show"/>
                <field name="th_origin_ids" widget="many2many_tags" optional="show" invisible="1"/>
                <field name="th_origin_id" optional="show"/>
                <field name="th_product_ids" widget="many2many_tags" optional="show"/>
                <field name="th_product_category_ids" string="Nhóm sản phẩm" widget="many2many_tags" optional="show"/>
                <field name="th_user_id" optional="show"/>
                <field name="th_status_category_id" optional="show"/>
                <field name="th_status_detail_id" optional="show"/>
                <field name="th_processing_status_id" optional="show"/>
                <field name="th_stage_id" optional="show"/>
                <field name="th_partner_reference_id" optional="show"/>
                <field name="th_ownership_unit_id" optional="hide"/>
                <field name="th_campaign_id" optional="hide"/>
                <field name="th_status_payment" optional="hide"/>
                <field name="th_detail_resign_id" optional="hide"/>
                <field name="th_day_recent_purchase" optional="hide"/>
                <field name="create_date" optional="hide"/>
                <field name="create_uid" string="Người tạo cơ hội" optional="hide"/>
                <field name="th_first_day_purchase" optional="hide"/>
                <field name="th_estimated_level_up_date" string="Ngày dự kiến lên L8" optional="show"/>
                <field name="th_auto_update_products" invisible="1"/>
            </tree>
        </field>
    </record>

    <!-- Search View cho Cơ hội sau bán -->

    <!-- Action cho Cơ hội sau bán -->
     <record id="th_apm_vstep_lead_after_sale_action" model="ir.actions.server">
        <field name="name">Cơ hội sau bán</field>
        <field name="model_id" ref="th_apm.model_th_apm"/>
        <field name="state">code</field>
        <field name="code">action = model.th_action_view_apm_vstep_lead_after_sale()</field>
    </record>
</odoo>