<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <function name="write" model="ir.model.data">
            <function name="search" model="ir.model.data">
                <value eval="[('module', '=', 'crm'), ('name', '=', 'crm_rule_personal_lead')]"/>
            </function>
            <value eval="{'noupdate': False}"/>
    </function>
    <record id="crm.crm_rule_personal_lead" model="ir.rule">
        <field name="active">False</field>
    </record>
    <function name="write" model="ir.model.data">
            <function name="search" model="ir.model.data">
                <value eval="[('module', '=', 'crm'), ('name', '=', 'crm_rule_personal_lead')]"/>
            </function>
            <value eval="{'noupdate': True}"/>
    </function>

    <function name="write" model="ir.model.data">
            <function name="search" model="ir.model.data">
                <value eval="[('module', '=', 'crm'), ('name', '=', 'crm_rule_all_lead')]"/>
            </function>
            <value eval="{'noupdate': False}"/>
    </function>
    <record id="crm.crm_rule_all_lead" model="ir.rule">
        <field name="active">False</field>
    </record>
    <function name="write" model="ir.model.data">
            <function name="search" model="ir.model.data">
                <value eval="[('module', '=', 'crm'), ('name', '=', 'crm_rule_all_lead')]"/>
            </function>
            <value eval="{'noupdate': True}"/>
    </function>

    <record id="th_module_category_crm_other" model="ir.module.category">
        <field name="name">CRM Bổ sung</field>
        <field name="sequence">5</field>
    </record>

    <record id="th_group_crm_cskh" model="res.groups">
        <field name="name">Chăm sóc khách hàng</field>
        <field name="category_id" ref="th_module_category_crm_other"/>
    </record>

    <record id="th_group_crm_tvts" model="res.groups">
        <field name="name">TVTS - Xét tuyển</field>
        <field name="category_id" ref="th_module_category_crm_other"/>
    </record>

    <record id="th_group_crm_ttdl" model="res.groups">
        <field name="name">Quản lý hồ sơ</field>
        <field name="category_id" ref="th_module_category_crm_other"/>
    </record>

    <record id="th_group_crm_can_create" model="res.groups">
        <field name="name">Tạo và theo dõi cơ hội</field>
        <field name="category_id" ref="th_module_category_crm_other"/>
    </record>

    <record id="th_module_category_crm" model="ir.module.category">
        <field name="name">Quản lý quan hệ khách hàng</field>
        <field name="description">Phân quyền quản lý CRM</field>
        <field name="sequence">4</field>
    </record>

    <record id="th_module_category_crm_root" model="ir.module.category">
        <field name="name">CRM</field>
        <field name="parent_id" ref="th_module_category_crm"/>
        <field name="sequence">5</field>
    </record>

    <record id="th_group_user_crm" model="res.groups">
        <field name="name">Nhân viên</field>
        <field name="implied_ids" eval="[(4, ref('sales_team.group_sale_salesman')), (3, ref('sales_team.group_sale_salesman_all_leads'))]"/>
        <field name="category_id" ref="th_module_category_crm_root"/>
    </record>

    <record id="th_group_leader_crm" model="res.groups">
        <field name="name">Trưởng nhóm</field>
        <field name="implied_ids" eval="[(4, ref('sales_team.group_sale_salesman')), (4, ref('th_group_user_crm'))]"/>
        <field name="category_id" ref="th_module_category_crm_root"/>
    </record>

    <record id="th_group_admin_crm" model="res.groups">
        <field name="name">Quản trị viên</field>
        <field name="implied_ids" eval="[(4, ref('sales_team.group_sale_manager')),
        (4, ref('th_group_leader_crm')), (4, ref('th_group_crm_cskh')), (4, ref('th_group_crm_tvts')),
        (4, ref('th_group_crm_ttdl')), (4, ref('th_group_crm_can_create'))]"/>
        <field name="category_id" ref="th_module_category_crm_root"/>
    </record>

    <record id="crm_group_partner_manager" model="res.groups">
        <field name="name">CRM: Theo dõi cơ hội của đối tác</field>
        <field name="category_id" ref="base.module_category_hidden"/>
<!--        <field name="implied_ids" eval="[(4, ref('th_group_user_crm'))]"/>-->
    </record>

    <record id="crm_group_partner_manager_rule" model="ir.rule">
        <field name="name">CRM: Được phép xem cơ hội thuộc sở hữu đối tác</field>
        <field name="model_id" ref="model_crm_lead"/>
        <field name="groups" eval="[(4, ref('crm_group_partner_manager'))]"/>
        <field name="domain_force">[('th_ownership_id.th_type', '=', 'other')]</field>
        <field name="perm_read" eval="1"/>
        <field name="perm_write" eval="0"/>
        <field name="perm_create" eval="0"/>
        <field name="perm_unlink" eval="0"/>
    </record>

    <record id="th_group_user_crm_rule" model="ir.rule">
        <field name="name">th_group_user_crm_rule</field>
        <field name="model_id" ref="model_crm_lead"/>
        <field name="groups" eval="[(4, ref('th_group_user_crm'))]"/>
        <field name="domain_force">[('user_id', '=', user.id)]</field>
    </record>

    <record id="th_group_leader_crm_rule" model="ir.rule">
        <field name="name">th_group_leader_crm</field>
        <field name="model_id" ref="model_crm_lead"/>
        <field name="groups" eval="[(4, ref('th_group_leader_crm'))]"/>
        <field name="domain_force">['|', '|', '|', ('user_id.th_manager_crm_ids', 'in', user.ids), ('create_uid', '=', user.id), ('user_id', '=', user.id), ('th_origin_id.th_program_management_crm_ids', 'in', user.ids)]</field>
    </record>

    <record id="th_group_admin_crm_rule" model="ir.rule">
        <field name="name">th_group_admin_crm_rule</field>
        <field name="model_id" ref="model_crm_lead"/>
        <field name="groups" eval="[(4, ref('th_group_admin_crm'))]"/>
        <field name="domain_force">[(1, '=', 1)]</field>
    </record>


    <record id="th_group_crm_can_create_rule" model="ir.rule">
        <field name="name">th_group_crm_can_create_rule</field>
        <field name="model_id" ref="model_crm_lead"/>
        <field name="groups" eval="[(4, ref('th_group_crm_can_create'))]"/>
        <field name="domain_force">['&amp;','&amp;',('th_ownership_id.th_active_team', '!=',False),
                                        '|',('th_ownership_id.th_active_team.member_ids', 'in',user.ids),('th_ownership_id.th_active_team.user_id', '=', user.id),
                                        '|',('th_create_user_checked_id','=', user.id),
                                            '|',('create_uid','=',user.id),
                                                ('th_origin_id.th_mkt_user_ids','in', user.ids)]
        </field>
    </record>

    <record id="th_group_ttdl_crm_rule" model="ir.rule">
        <field name="name">th_group_ttdl_crm_rule</field>
        <field name="model_id" ref="model_crm_lead"/>
        <field name="groups" eval="[(4, ref('th_group_crm_ttdl'))]"/>
        <field name="domain_force">[('th_student_profile_id', '!=', False)]</field>
    </record>

    <record id="th_group_crm_cskh_rule" model="ir.rule">
        <field name="name">th_group_crm_cskh_rule</field>
        <field name="model_id" ref="model_crm_lead"/>
        <field name="groups" eval="[(4, ref('th_group_crm_cskh'))]"/>
        <field name="domain_force">['|', '|', '|', '|','|',('user_id.th_manager_crm_ids', 'in', user.ids),
            ('user_id', '=', user.id),
            ('th_ownership_id.th_active_team.member_ids', 'in', user.ids),
            ('th_ownership_id.th_active_team.user_id', '=', user.id),
            ('th_ownership_id.th_customer_care_team.member_ids', 'in',user.ids),
            ('th_ownership_id.th_customer_care_team.user_id', '=', user.id)]
        </field>
    </record>

    <record id="th_group_leader_sale_order_rule" model="ir.rule">
        <field name="name">th_group_leader_sale_order_rule</field>
        <field name="model_id" ref="model_sale_order"/>
        <field name="groups" eval="[(4, ref('th_group_leader_crm'))]"/>
        <field name="domain_force">['|', ('opportunity_id.user_id.th_manager_crm_ids', 'in', user.ids), ('opportunity_id.user_id', '=', user.id)]</field>
    </record>

    <record id="th_group_user_crm_profile_rule" model="ir.rule">
        <field name="name">th_group_user_crm_profile_rule</field>
        <field name="model_id" ref="model_th_student_profile"/>
        <field name="groups" eval="[(4, ref('th_group_user_crm'))]"/>
        <field name="domain_force">['|',('th_lead_id.user_id', '=', user.id),('th_origin_id.th_profile_crm_ids', 'in', user.ids)]</field>
    </record>

    <record id="th_group_leader_crm_profile_rule" model="ir.rule">
        <field name="name">th_group_leader_crm_profile_rule</field>
        <field name="model_id" ref="model_th_student_profile"/>
        <field name="groups" eval="[(4, ref('th_group_leader_crm'))]"/>
        <field name="domain_force">['|', ('th_lead_id.user_id.th_manager_crm_ids', 'in', user.ids), ('th_lead_id.user_id', '=', user.id)]</field>
    </record>

    <record id="th_group_admin_crm_profile_rule" model="ir.rule">
        <field name="name">th_group_admin_crm_profile_rule</field>
        <field name="model_id" ref="model_th_student_profile"/>
        <field name="groups" eval="[(4, ref('th_group_admin_crm')), (4, ref('th_group_crm_ttdl'))]"/>
        <field name="domain_force">[(1, '=', 1)]</field>
    </record>

    <record id="th_group_user_ccs_rule" model="ir.rule">
        <field name="name">th_group_user_ccs_rule</field>
        <field name="model_id" ref="model_ccs_lead"/>
        <field name="groups" eval="[(4, ref('th_group_user_crm'))]"/>
        <field name="domain_force">['|', ('create_uid', '=', user.id), ('th_user_id', '=', user.id)]</field>
    </record>

    <record id="th_group_leader_ccs_rule" model="ir.rule">
        <field name="name">th_group_leader_ccs_rule</field>
        <field name="model_id" ref="model_ccs_lead"/>
        <field name="groups" eval="[(4, ref('th_group_leader_crm'))]"/>
        <field name="domain_force">['|', '|', ('th_user_id.th_manager_crm_ids', 'in', user.ids), ('create_uid', '=', user.id), ('th_user_id', '=', user.id)]</field>
    </record>

    <record id="th_group_admin_ccs_rule" model="ir.rule">
        <field name="name">th_group_admin_ccs_rule</field>
        <field name="model_id" ref="model_ccs_lead"/>
        <field name="groups" eval="[(4, ref('th_group_admin_crm'))]"/>
        <field name="domain_force">[(1, '=', 1)]</field>
    </record>

<!--    Quyền lịch làm việc     -->

    <record id="th_summary_activity_user_rule" model="ir.rule">
        <field name="name">Người dùng chỉ xem activity của chính họ</field>
        <field name="model_id" ref="model_th_summary_activity"/>
        <field name="domain_force">['|', ('user_id', '=', user.id), ('th_lead_id.user_id', '=', user.id)]</field>
        <field name="groups" eval="[(4, ref('th_crm.th_group_user_crm'))]"/>
    </record>

    <record id="th_summary_activity_leader_rule" model="ir.rule">
        <field name="name">Trưởng nhóm - Xem lịch làm việc của cấp dưới</field>
        <field name="model_id" ref="model_th_summary_activity"/>
        <field name="domain_force">['|','|',
            ('user_id.th_manager_crm_ids', 'in', user.ids),
            ('user_id', '=', user.id),
            ('th_lead_id.th_origin_id.th_program_management_crm_ids', 'in', user.ids)]
        </field>
        <field name="groups" eval="[(4, ref('th_crm.th_group_leader_crm'))]"/>
    </record>

    <record id="th_summary_activity_admin_rule" model="ir.rule">
        <field name="name">admin có thể xem tất activity</field>
        <field name="model_id" ref="model_th_summary_activity"/>
        <field name="domain_force">[(1, '=', 1)]</field>
        <field name="groups" eval="[(4, ref('th_crm.th_group_admin_crm'))]"/>
    </record>

    <record id="th_summary_activity_cskh_rule" model="ir.rule">
        <field name="name">th_summary_activity_cskh_rule</field>
        <field name="model_id" ref="model_th_summary_activity"/>
        <field name="groups" eval="[(4, ref('th_group_crm_cskh'))]"/>
        <field name="domain_force">['|', '|', '|', '|','|',('user_id.th_manager_crm_ids', 'in', user.ids),
            ('user_id', '=', user.id),
            ('th_lead_id.th_ownership_id.th_active_team.member_ids', 'in', user.ids),
            ('th_lead_id.th_ownership_id.th_active_team.user_id', '=', user.id),
            ('th_lead_id.th_ownership_id.th_customer_care_team.member_ids', 'in',user.ids),
            ('th_lead_id.th_ownership_id.th_customer_care_team.user_id', '=', user.id)]
        </field>
    </record>

    <record id="th_summary_activity_can_create_rule" model="ir.rule">
        <field name="name">th_summary_activity_can_create_rule</field>
        <field name="model_id" ref="model_th_summary_activity"/>
        <field name="groups" eval="[(4, ref('th_group_crm_can_create'))]"/>
        <field name="domain_force">['&amp;','&amp;',('th_lead_id.th_ownership_id.th_active_team', '!=',False),
                                        '|',('th_lead_id.th_ownership_id.th_active_team.member_ids', 'in',user.ids),('th_lead_id.th_ownership_id.th_active_team.user_id', '=', user.id),
                                        '|',('th_lead_id.th_create_user_checked_id','=', user.id),
                                            '|',('create_uid','=',user.id),
                                                ('th_lead_id.th_origin_id.th_mkt_user_ids','in', user.ids)]
        </field>
    </record>

</odoo>
