from odoo.tests.common import TransactionCase, tagged

@tagged('test_crm')
class TestCrmLeadCreate(TransactionCase):

    def setUp(self):
        self.Partner2 = self.env['res.partner'].search([('phone', '=', '0377450757')], limit=1)
        self.School = self.env['th.origin'].search([('name', '=', '<PERSON><PERSON><PERSON> viện <PERSON>')], limit=1)
        self.School2 = self.env['th.origin'].search([('name', '=', 'Đ<PERSON><PERSON> họ<PERSON> Lâm')], limit=1)
        self.StatusCategory = self.env['th.status.category'].search([('name', '=', 'Chưa xử lý'), ('th_type', '=', 'crm')], limit=1)
        self.StatusDetail = self.env['th.status.detail'].search([('name', '=', 'Chưa xử lý1')], limit=1)
        self.StatusDetail2 = self.env['th.status.detail'].search([('name', '=', '<PERSON> đối tượng.CRM')], limit=1)
        self.OwnershipUnit = self.env['th.ownership.unit'].search([('name', '=', 'AUM')], limit=1)
        self.Channel = self.env['th.info.channel'].search([('name', '=', 'Lọc')], limit=1)
        self.SourceGroup = self.env['th.source.group'].search([('name', '=', 'GIỚI THIỆU')], limit=1)


    """ Kiểm tra tạo cơ hội thủ công khi chưa có liên hệ """
    def test_crm_lead_create_without_contact(self):
        self.Partner = self.env['res.partner'].create({
            'name': 'test',
            'phone': '0955300555',
        })
        lead = self.env['crm.lead'].create({
            'partner_id': self.Partner.id,  # Khách hàng
            'th_origin_id': self.School.id,  # Trường học (Model th.origin)
            'th_status_group_id': self.StatusCategory.id,  # Nhóm tình trạng cơ hội (Many2one đến th.status.category)
            'th_status_detail_id': self.StatusDetail.id, # Trạng thái chi tiết (th.status.detail)
            'th_ownership_id': self.OwnershipUnit.id,  # Đơn vị sở hữu (Many2one đến th.ownership.unit)
            'th_channel_id': self.Channel.id,  # Kênh (Many2one đến th.info.channel)
            'th_source_group_id': self.SourceGroup.id,  # Nhóm nguồn (Many2one đến th.source.group)
        })
        # Kiểm tra kết quả
        self.assertTrue(lead.id, "Không tạo được bản ghi cơ hội")
        self.assertEqual(lead.partner_id.name, 'test')
        self.assertEqual(lead.phone, '0955300555')
        self.assertEqual(lead.th_origin_id.name, 'Học viện Tài Chính')
        self.assertEqual(lead.th_status_group_id.name, 'Chưa xử lý')
        self.assertEqual(lead.th_status_detail_id.name, 'Chưa xử lý.CRM')
        self.assertEqual(lead.th_ownership_id.name, 'AUM')
        self.assertEqual(lead.th_channel_id.name, 'Lọc')
        self.assertEqual(lead.th_source_group_id.name, 'GIỚI THIỆU')


    """ Kiểm tra tạo cơ hội thủ công khi đã có liên hệ và chưa có cơ hội CRM """
    def test_crm_lead_create_with_contact(self):
        lead2 = self.env['crm.lead'].create({
            'partner_id': self.Partner2.id,
            'th_origin_id': self.School2.id,
            'th_status_group_id': self.StatusCategory.id,
            'th_status_detail_id': self.StatusDetail.id,
            'th_ownership_id': self.OwnershipUnit.id,
            'th_channel_id': self.Channel.id,
            'th_source_group_id': self.SourceGroup.id,
        })
        # Kiểm tra kết quả
        self.assertTrue(lead2.id, "Không tạo được bản ghi cơ hội")
        self.assertEqual(lead2.phone, '0377450757')
        self.assertEqual(lead2.th_origin_id.name, 'Đại học Nông Lâm')
        self.assertEqual(lead2.th_status_group_id.name, 'Chưa xử lý')
        self.assertEqual(lead2.th_status_detail_id.name, 'Chưa xử lý.CRM')
        self.assertEqual(lead2.th_ownership_id.name, 'AUM')
        self.assertEqual(lead2.th_channel_id.name, 'Lọc')
        self.assertEqual(lead2.th_source_group_id.name, 'GIỚI THIỆU')


    """ Kiểm tra tạo cơ hội thủ công khi đã có liên hệ và đã có cơ hội CRM
        Cơ hội vừa Check trùng thua """
    def test_crm_lead_create_with_contact_fail_check_condition(self):
        lead3 = self.env['crm.lead'].create({
            'partner_id': self.Partner2.id,
            'th_origin_id': self.School.id,
            'th_status_group_id': self.StatusCategory.id,
            'th_status_detail_id': self.StatusDetail.id,
            'th_ownership_id': self.OwnershipUnit.id,
            'th_channel_id': self.Channel.id,
            'th_source_group_id': self.SourceGroup.id,
        })
        # Kiểm tra kết quả
        self.assertTrue(lead3.id, "Không tạo được bản ghi cơ hội")
        self.assertEqual(lead3.th_is_a_duplicate_opportunity, True) #cơ hội mới thua


    """ Kiểm tra tạo cơ hội thủ công khi đã có liên hệ và đã có cơ hội CRM
        Cơ hội vừa Check trùng thắng """
    def test_crm_lead_create_with_contact_win_check_condition(self):
        old_lead4 = self.env['crm.lead'].search([('phone', '=', '0377450757')], limit=1)
        if old_lead4:
            old_lead4.write({
                'stage_id': self.env['crm.stage'].search([('name', '=', 'L1')]).id,
                'th_status_group_id': self.env['th.status.category'].search([('name', '=', 'Ngừng chăm sóc'), ('th_type', '=', 'crm')]).id,
                'th_status_detail_id': self.env['th.status.detail'].search([('name', '=', 'Sai đối tượng.CRM')]).id
            })
        lead4 = self.env['crm.lead'].create({
            'partner_id': self.Partner2.id,
            'th_origin_id': self.School.id,
            'th_status_group_id': self.StatusCategory.id,
            'th_status_detail_id': self.StatusDetail.id,
            'th_ownership_id': self.OwnershipUnit.id,
            'th_channel_id': self.Channel.id,
            'th_source_group_id': self.SourceGroup.id,
        })
        # Kiểm tra kết quả
        self.assertTrue(lead4.id, "Không tạo được bản ghi cơ hội")
        self.assertEqual(lead4.th_is_a_duplicate_opportunity, False) #cơ hội mới thắng
        if old_lead4:
            self.assertEqual(old_lead4.th_is_a_duplicate_opportunity, True) #cơ hội cũ thua
