import base64
import xlsxwriter
from io import BytesIO
from datetime import datetime
from collections import defaultdict

from odoo import models, fields, api, _
from odoo.exceptions import ValidationError


class ThCourseReportWizard(models.TransientModel):
    _name = 'th.course.report.wizard'
    _description = '<PERSON> <PERSON><PERSON><PERSON> cáo thống kê khóa học'

    # Tiêu chí lọc báo cáo
    th_program_type = fields.Selection([
        ('all', 'Tất cả'),
        ('short', 'Ngắn hạn'), 
        ('long', 'Dài hạn')
    ], string='Chương trình', default='all', required=True)
    
    th_production_office_id = fields.Many2one(
        'th.office.production', 
        string='Phòng sản xuất',
        help='Để trống nếu muốn lọc tất cả phòng sản xuất'
    )
    
    th_responsible_user_id = fields.Many2one(
        'res.users',
        string='Người phụ trách',
        help='<PERSON><PERSON> trống nếu muốn lọc tất cả người phụ trách'
    )
    
    def th_generate_report(self):
        """Tạo báo cáo thống kê khóa học"""
        # Tạo domain filter
        domain = self._th_build_domain()

        # Kiểm tra có dữ liệu không
        courses = self.env['th.course.lpm2'].search(domain)

        if not courses:
            raise ValidationError(_('Không tìm thấy khóa học nào phù hợp với tiêu chí lọc.'))

        # Hiển thị kết quả trực tiếp trong pivot/graph view
        return self._th_show_pivot_graph_result()

    def _th_build_domain(self):
        """Xây dựng domain filter cho khóa học"""
        domain = []

        # Lọc theo loại chương trình
        if self.th_program_type != 'all':
            domain.append(('th_type_course', '=', self.th_program_type))

        # Lọc theo phòng sản xuất
        if self.th_production_office_id:
            domain.append(('th_project_id.th_production_office_id', '=', self.th_production_office_id.id))

        # Lọc theo người phụ trách chính
        if self.th_responsible_user_id:
            domain.append(('th_user_id', '=', self.th_responsible_user_id.id))

        return domain

    def _th_show_pivot_graph_result(self):
        """Hiển thị kết quả báo cáo trong pivot/graph view sử dụng trực tiếp model th.course.lpm2"""
        # Lấy domain filter từ wizard
        domain = self._th_build_domain()

        context = []
        
        # Tự động set group by dựa trên bộ lọc đã chọn
        # Ưu tiên: loại chương trình > phòng sản xuất > người phụ trách
        if self.th_program_type != 'all':
            context['search_default_group_by_program_type'] = 1
        elif self.th_production_office_id:
            context['search_default_group_by_production_office'] = 1
        elif self.th_responsible_user_id:
            context['search_default_group_by_responsible_user'] = 1
        else:
            # Mặc định group by program type nếu không có filter nào
            context['search_default_group_by_program_type'] = 1

        # Thêm thông tin bộ lọc vào context để hiển thị
        filter_info = []
        if self.th_program_type != 'all':
            program_display = dict(self._fields['th_program_type'].selection).get(self.th_program_type)
            filter_info.append(f'Chương trình: {program_display}')
        if self.th_production_office_id:
            filter_info.append(f'Phòng sản xuất: {self.th_production_office_id.name}')
        if self.th_responsible_user_id:
            filter_info.append(f'Người phụ trách: {self.th_responsible_user_id.name}')

        context['th_filter_info'] = ' | '.join(filter_info)

        return {
            'type': 'ir.actions.act_window',
            'name': _('Phân tích thống kê khóa học'),
            'res_model': 'th.course.lpm2',
            'view_mode': 'pivot,graph',
            'view_ids': [
                (self.env.ref('th_lpm2.th_course_lpm2_pivot_report').id, 'pivot'),
                (self.env.ref('th_lpm2.th_course_lpm2_graph_report').id, 'graph'),
            ],
            'domain': domain,
            ''
            'target': 'current',
            'search_view_id': self.env.ref('th_lpm2.th_course_lpm2_search_report').id,
        }

    def _th_show_report_result(self):
        """Hiển thị kết quả báo cáo (method cũ - giữ lại cho tương thích)"""
        return {
            'type': 'ir.actions.act_window',
            'name': _('Kết quả báo cáo thống kê khóa học'),
            'res_model': 'th.course.report.wizard',
            'view_mode': 'form',
            'res_id': self.id,
            'target': 'new',
            'context': {'show_report_result': True}
        }

    class ThCourseReport(models.TransientModel):
        _name="th.course.report.line"

    # def th_export_excel(self):
    #     """Xuất báo cáo ra file Excel"""
    #     # Tạo file Excel
    #     output = BytesIO()
    #     workbook = xlsxwriter.Workbook(output)
    #     worksheet = workbook.add_worksheet('Báo cáo thống kê khóa học')

    #     # Định dạng header
    #     header_format = workbook.add_format({
    #         'bold': True,
    #         'bg_color': '#D7E4BC',
    #         'border': 1,
    #         'align': 'center',
    #         'valign': 'vcenter'
    #     })

    #     # Định dạng dữ liệu
    #     data_format = workbook.add_format({
    #         'border': 1,
    #         'align': 'left',
    #         'valign': 'vcenter'
    #     })

    #     # Định dạng số
    #     number_format = workbook.add_format({
    #         'border': 1,
    #         'align': 'center',
    #         'valign': 'vcenter',
    #         'num_format': '#,##0'
    #     })

    #     # Headers
    #     headers = [
    #         'Tên nhóm',
    #         'Loại chương trình',
    #         'Phòng sản xuất',
    #         'Người phụ trách',
    #         'Số lượng khóa học',
    #     ]

    #     # Viết tiêu đề báo cáo
    #     worksheet.merge_range('A1:E1', 'BÁO CÁO THỐNG KÊ SỐ LƯỢNG KHÓA HỌC', header_format)
    #     worksheet.write(1, 0, f'Ngày tạo: {datetime.now().strftime("%d/%m/%Y %H:%M:%S")}', data_format)

    #     # Thông tin bộ lọc
    #     filter_info = []
    #     if self.th_program_type != 'all':
    #         program_display = dict(self._fields['th_program_type'].selection).get(self.th_program_type)
    #         filter_info.append(f'Chương trình: {program_display}')
    #     if self.th_production_office_id:
    #         filter_info.append(f'Phòng sản xuất: {self.th_production_office_id.name}')
    #     if self.th_responsible_user_id:
    #         filter_info.append(f'Người phụ trách: {self.th_responsible_user_id.name}')

    #     if filter_info:
    #         worksheet.write(2, 0, f'Bộ lọc: {" | ".join(filter_info)}', data_format)

    #     # Viết headers
    #     header_row = 4
    #     for col, header in enumerate(headers):
    #         worksheet.write(header_row, col, header, header_format)
    #         worksheet.set_column(col, col, 25)  # Set column width

    #     # Viết dữ liệu
    #     for row, line in enumerate(self.th_report_line_ids, header_row + 1):
    #         worksheet.write(row, 0, line.th_group_name or '', data_format)
    #         worksheet.write(row, 1, line.th_program_type_display or '', data_format)
    #         worksheet.write(row, 2, line.th_production_office or '', data_format)
    #         worksheet.write(row, 3, line.th_responsible_user or '', data_format)
    #         worksheet.write(row, 4, line.th_course_count, number_format)

    #     # Viết tổng kết
    #     total_row = header_row + len(self.th_report_line_ids) + 1
    #     worksheet.write(total_row, 3, 'TỔNG CỘNG:', header_format)
    #     worksheet.write(total_row, 4, self.th_total_courses, number_format)

    #     workbook.close()
    #     excel_data = output.getvalue()

    #     # Tạo attachment để download
    #     filename = f'bao_cao_thong_ke_khoa_hoc_{datetime.now().strftime("%Y%m%d_%H%M%S")}.xlsx'
    #     attachment = self.env['ir.attachment'].create({
    #         'name': filename,
    #         'type': 'binary',
    #         'datas': base64.b64encode(excel_data),
    #         'res_model': 'th.course.report.wizard',
    #         'res_id': self.id,
    #         'mimetype': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
    #     })

    #     return {
    #         'type': 'ir.actions.act_url',
    #         'url': f'/web/content/{attachment.id}?download=true',
    #         'target': 'self',
    #     }



