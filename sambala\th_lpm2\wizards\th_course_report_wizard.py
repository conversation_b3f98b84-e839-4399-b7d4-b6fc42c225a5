import base64
import xlsxwriter
from io import BytesIO
from datetime import datetime
from collections import defaultdict

from odoo import models, fields, api, _
from odoo.exceptions import ValidationError


class ThCourseReportWizard(models.TransientModel):
    _name = 'th.course.report.wizard'
    _description = '<PERSON> <PERSON><PERSON><PERSON> cáo thống kê khóa học'

    # Tiêu chí lọc báo cáo
    th_program_type = fields.Selection([
        ('all', 'Tất cả'),
        ('short', 'Ngắn hạn'), 
        ('long', 'Dài hạn')
    ], string='Chương trình', default='all', required=True)
    
    th_production_office_ids = fields.Many2many(
        'th.office.production', 
        string='Phòng sản xuất',
        help='Để trống nếu muốn lọc tất cả phòng sản xuất'
    )
    
    th_responsible_user_ids = fields.Many2many(
        'res.users',
        string='Người phụ trách',
        help='<PERSON><PERSON> trống nếu muốn lọc tất cả người phụ trách'
    )
    
    th_major_ids = fields.Many2many('th.major', string='<PERSON><PERSON>nh học')
    
    th_origin_ids = fields.Many2many(
        'th.origin',
        string='Đơn vị sử dụng',
        help='Để trống nếu muốn lọc tất cả đơn vị sử dụng'
    )
    
    th_project_ids = fields.Many2many(
        'th.project.lpm2',
        string='Dự án',
        help='Để trống nếu muốn lọc tất cả dự án'
    )
    
    def th_generate_report(self):
        """Tạo báo cáo thống kê khóa học"""
        # Tạo domain filter
        domain = self._th_build_domain()

        # Kiểm tra có dữ liệu không
        courses = self.env['th.course.lpm2'].search(domain)

        if not courses:
            raise ValidationError(_('Không tìm thấy khóa học nào phù hợp với tiêu chí lọc.'))

        # Hiển thị kết quả trực tiếp trong pivot/graph view
        return self._th_show_pivot_graph_result()

    def _th_build_domain(self):
        """Xây dựng domain filter cho khóa học"""
        domain = []

        # Lọc theo loại chương trình
        if self.th_program_type != 'all':
            domain.append(('th_type_course', '=', self.th_program_type))

        # Lọc theo phòng sản xuất
        if self.th_production_office_ids:
            domain.append(('th_project_id.th_production_office_id', 'in', self.th_production_office_ids.ids))

        # Lọc theo người phụ trách chính
        if self.th_responsible_user_ids:
            domain.append(('th_user_id', 'in', self.th_responsible_user_ids.ids))
        # Lọc theo dự án
        if self.th_project_ids:
            domain.append(('th_project_id', 'in', self.th_project_ids.ids))

        # Lọc theo đơn vị sử dụng
        if self.th_origin_ids:
            domain.append(('th_origin_id', 'in', self.th_origin_ids.ids))

        # Lọc theo ngành học
        if self.th_major_ids:
            domain.append(('th_major_ids', 'in', self.th_major_ids.ids))

        return domain

    @api.constrains('th_responsible_user_ids', 'th_major_ids')
    def _check_exclusive_choice(self):
        """Validation: Chỉ được chọn 1 trong 2 - người phụ trách hoặc ngành học"""
        for record in self:
            if record.th_responsible_user_ids and record.th_major_ids:
                raise ValidationError(_('Chỉ được chọn một trong hai: Người phụ trách hoặc Ngành học, không được chọn cả hai.'))

    def _th_show_pivot_graph_result(self):
        """Hiển thị kết quả báo cáo trong pivot/graph view sử dụng trực tiếp model th.course.lpm2"""
        # Lấy domain filter từ wizard
        domain = self._th_build_domain()

        context = {}
        
        # Tự động set group by dựa trên bộ lọc đã chọn
        # Thứ tự ưu tiên: loại chương trình > đơn vị sử dụng > phòng sản xuất > người phụ trách/ngành học
        if self.th_program_type:
            context['search_default_group_by_program_type'] = 1
            context['search_default_group_by_origin'] = 1
            context['search_default_group_by_production_office'] = 1
            context['search_default_group_by_major'] = 1
        elif self.th_origin_ids:
            context['search_default_group_by_origin'] = 1
            context['search_default_group_by_production_office'] = 1
            context['search_default_group_by_major'] = 1
        elif self.th_production_office_ids:
            context['search_default_group_by_production_office'] = 1
            context['search_default_group_by_responsible_user'] = 1
        elif self.th_responsible_user_ids:
            context['search_default_group_by_responsible_user'] = 1
        elif self.th_major_ids:
            context['search_default_group_by_major'] = 1
        else:
            # Mặc định group by program type nếu không có filter nào
            context['search_default_group_by_program_type'] = 1

        # Thêm thông tin bộ lọc vào context để hiển thị
        filter_info = []
        if self.th_program_type != 'all':
            program_display = dict(self._fields['th_program_type'].selection).get(self.th_program_type)
            filter_info.append(f'Chương trình: {program_display}')

        # Xử lý Many2many fields cho phòng sản xuất
        if self.th_production_office_ids:
            office_names = ', '.join(self.th_production_office_ids.mapped('name'))
            filter_info.append(f'Phòng sản xuất: {office_names}')

        # Xử lý Many2many fields cho người phụ trách
        if self.th_responsible_user_ids:
            user_names = ', '.join(self.th_responsible_user_ids.mapped('name'))
            filter_info.append(f'Người phụ trách: {user_names}')

        # Xử lý Many2many fields cho dự án
        if self.th_project_ids:
            project_names = ', '.join(self.th_project_ids.mapped('name'))
            filter_info.append(f'Dự án: {project_names}')

        # Xử lý Many2many fields cho đơn vị sử dụng
        if self.th_origin_ids:
            origin_names = ', '.join(self.th_origin_ids.mapped('name'))
            filter_info.append(f'Đơn vị sử dụng: {origin_names}')

        # Xử lý Many2many fields cho ngành học
        if self.th_major_ids:
            major_names = ', '.join(self.th_major_ids.mapped('name'))
            filter_info.append(f'Ngành học: {major_names}')

        context['th_filter_info'] = ' | '.join(filter_info)

        return {
            'type': 'ir.actions.act_window',
            'name': _('Phân tích thống kê khóa học'),
            'res_model': 'th.course.lpm2',
            'view_mode': 'pivot,graph',
            'view_ids': [
                (self.env.ref('th_lpm2.th_course_lpm2_pivot_report').id, 'pivot'),
                (self.env.ref('th_lpm2.th_course_lpm2_graph_report').id, 'graph'),
            ],
            'domain': domain,
            'context': context,
            'target': 'current',
            'search_view_id': [self.env.ref('th_lpm2.th_course_lpm2_search_report').id],
        }

