import base64
import xlsxwriter
from io import BytesIO
from datetime import datetime
from collections import defaultdict

from odoo import models, fields, api, _
from odoo.exceptions import ValidationError


class ThCourseReportWizard(models.TransientModel):
    _name = 'th.course.report.wizard'
    _description = '<PERSON> <PERSON><PERSON><PERSON> cáo thống kê khóa học'

    # Tiêu chí lọc báo cáo
    th_program_type = fields.Selection([
        ('all', 'Tất cả'),
        ('short', 'Ngắn hạn'), 
        ('long', 'Dài hạn')
    ], string='Chương trình', default='all', required=True)
    
    th_production_office_id = fields.Many2one(
        'th.office.production', 
        string='Phòng sản xuất',
        help='Để trống nếu muốn lọc tất cả phòng sản xuất'
    )
    
    th_responsible_user_id = fields.Many2one(
        'res.users',
        string='Người phụ trách',
        help='<PERSON><PERSON> trống nếu muốn lọc tất cả người phụ trách'
    )

    # Kết quả báo cáo
    th_report_line_ids = fields.One2many(
        'th.course.report.line', 
        'th_wizard_id', 
        string='Kết quả báo cáo'
    )
    
    th_total_courses = fields.Integer(
        string='Tổng số khóa học', 
        compute='_compute_total_courses'
    )

    @api.depends('th_report_line_ids')
    def _compute_total_courses(self):
        """Tính tổng số khóa học từ các dòng báo cáo"""
        for wizard in self:
            wizard.th_total_courses = sum(wizard.th_report_line_ids.mapped('th_course_count'))

    def th_generate_report(self):
        """Tạo báo cáo thống kê khóa học"""
        # Xóa kết quả báo cáo cũ
        self.th_report_line_ids.unlink()
        
        # Tạo domain filter
        domain = self._th_build_domain()
        
        # Lấy dữ liệu khóa học
        courses = self.env['th.course.lpm2'].search(domain)
        
        if not courses:
            raise ValidationError(_('Không tìm thấy khóa học nào phù hợp với tiêu chí lọc.'))
        
        # Nhóm dữ liệu theo tiêu chí
        grouped_data = self._th_group_courses(courses)
        
        # Tạo dòng báo cáo
        report_lines = []
        for group_key, course_list in grouped_data.items():
            line_vals = self._th_prepare_report_line(group_key, course_list)
            report_lines.append((0, 0, line_vals))
        
        self.th_report_line_ids = report_lines

        # Hiển thị kết quả trong pivot/graph view
        return self._th_show_pivot_graph_result()

    def _th_build_domain(self):
        """Xây dựng domain filter cho khóa học"""
        domain = []

        # Lọc theo loại chương trình
        if self.th_program_type != 'all':
            domain.append(('th_type_course', '=', self.th_program_type))

        # Lọc theo phòng sản xuất
        if self.th_production_office_id:
            domain.append(('th_project_id.th_production_office_id', '=', self.th_production_office_id.id))

        # Lọc theo người phụ trách chính
        if self.th_responsible_user_id:
            domain.append(('th_user_id', '=', self.th_responsible_user_id.id))

        return domain

    def _th_group_courses(self, courses):
        """Nhóm khóa học theo phòng sản xuất"""
        grouped_data = defaultdict(list)

        for course in courses:
            group_key = self._th_get_group_key(course)
            grouped_data[group_key].append(course)

        return grouped_data

    def _th_get_group_key(self, course):
        """Lấy key để nhóm khóa học theo phòng sản xuất"""
        office = course.th_project_id.th_production_office_id
        return office.name if office else _('Chưa có phòng sản xuất')

    def _th_prepare_report_line(self, group_key, course_list):
        """Chuẩn bị dữ liệu cho dòng báo cáo"""
        # Lấy thông tin chi tiết từ khóa học đầu tiên để làm mẫu
        sample_course = course_list[0] if course_list else None

        # Xác định program_type cho pivot view
        program_types = set()
        for course in course_list:
            if course.th_type_course:
                program_types.add(course.th_type_course)

        if len(program_types) == 1:
            th_program_type = list(program_types)[0]
        elif len(program_types) > 1:
            th_program_type = 'mixed'
        else:
            th_program_type = 'undefined'

        # Xác định production_office_id và responsible_user_id chính
        production_office_id = None
        responsible_user_id = None

        if sample_course:
            # Luôn lấy production_office_id nếu có
            if sample_course.th_project_id and sample_course.th_project_id.th_production_office_id:
                production_office_id = sample_course.th_project_id.th_production_office_id.id

            # Lấy responsible_user_id chính
            responsible_user_id = sample_course.th_user_id.id if sample_course.th_user_id else None

        return {
            'th_group_name': group_key,
            'th_program_type_display': self._th_get_program_type_display(course_list),
            'th_production_office': self._th_get_production_office_display(course_list),
            'th_responsible_user': self._th_get_responsible_user_display(course_list),
            'th_program_type': th_program_type,
            'th_production_office_id': production_office_id,
            'th_responsible_user_id': responsible_user_id,
            'th_course_count': len(course_list),
            'th_course_ids': [(6, 0, [c.id for c in course_list])]
        }

    def _th_get_program_type_display(self, course_list):
        """Lấy hiển thị loại chương trình"""
        types = set()
        for course in course_list:
            if course.th_type_course:
                type_display = dict(course._fields['th_type_course'].selection).get(
                    course.th_type_course, ''
                )
                if type_display:
                    types.add(type_display)
        return ', '.join(types) if types else _('Chưa xác định')

    def _th_get_production_office_display(self, course_list):
        """Lấy hiển thị phòng sản xuất"""
        offices = set()
        for course in course_list:
            office = course.th_project_id.th_production_office_id
            if office:
                offices.add(office.name)
        return ', '.join(offices) if offices else _('Chưa có phòng sản xuất')

    def _th_get_responsible_user_display(self, course_list):
        """Lấy hiển thị người phụ trách"""
        users = set()
        for course in course_list:
            user = course.th_user_id
            if user:
                users.add(user.name)
        return ', '.join(users) if users else _('Chưa có người phụ trách')

    def _th_show_pivot_graph_result(self):
        """Hiển thị kết quả báo cáo trong pivot/graph view"""
        # Tạo context với thông tin bộ lọc
        context = {
            'th_wizard_id': self.id,
            'search_default_group_by_program_type': 1,
        }

        # Thêm thông tin bộ lọc vào context để hiển thị
        filter_info = []
        if self.th_program_type != 'all':
            program_display = dict(self._fields['th_program_type'].selection).get(self.th_program_type)
            filter_info.append(f'Chương trình: {program_display}')
        if self.th_production_office_id:
            filter_info.append(f'Phòng sản xuất: {self.th_production_office_id.name}')
        if self.th_responsible_user_id:
            filter_info.append(f'Người phụ trách: {self.th_responsible_user_id.name}')

        context['th_filter_info'] = ' | '.join(filter_info)

        return {
            'type': 'ir.actions.act_window',
            'name': _('Phân tích thống kê khóa học'),
            'res_model': 'th.course.report.line',
            'view_mode': 'pivot,graph',
            'domain': [('th_wizard_id', '=', self.id)],
            'context': context,
            'target': 'current',
        }

    def _th_show_report_result(self):
        """Hiển thị kết quả báo cáo (method cũ - giữ lại cho tương thích)"""
        return {
            'type': 'ir.actions.act_window',
            'name': _('Kết quả báo cáo thống kê khóa học'),
            'res_model': 'th.course.report.wizard',
            'view_mode': 'form',
            'res_id': self.id,
            'target': 'new',
            'context': {'show_report_result': True}
        }

    def th_export_excel(self):
        """Xuất báo cáo ra file Excel"""
        if not self.th_report_line_ids:
            raise ValidationError(_('Vui lòng tạo báo cáo trước khi xuất Excel'))

        # Tạo file Excel
        output = BytesIO()
        workbook = xlsxwriter.Workbook(output)
        worksheet = workbook.add_worksheet('Báo cáo thống kê khóa học')

        # Định dạng header
        header_format = workbook.add_format({
            'bold': True,
            'bg_color': '#D7E4BC',
            'border': 1,
            'align': 'center',
            'valign': 'vcenter'
        })

        # Định dạng dữ liệu
        data_format = workbook.add_format({
            'border': 1,
            'align': 'left',
            'valign': 'vcenter'
        })

        # Định dạng số
        number_format = workbook.add_format({
            'border': 1,
            'align': 'center',
            'valign': 'vcenter',
            'num_format': '#,##0'
        })

        # Headers
        headers = [
            'Tên nhóm',
            'Loại chương trình',
            'Phòng sản xuất',
            'Người phụ trách',
            'Số lượng khóa học',
        ]

        # Viết tiêu đề báo cáo
        worksheet.merge_range('A1:E1', 'BÁO CÁO THỐNG KÊ SỐ LƯỢNG KHÓA HỌC', header_format)
        worksheet.write(1, 0, f'Ngày tạo: {datetime.now().strftime("%d/%m/%Y %H:%M:%S")}', data_format)

        # Thông tin bộ lọc
        filter_info = []
        if self.th_program_type != 'all':
            program_display = dict(self._fields['th_program_type'].selection).get(self.th_program_type)
            filter_info.append(f'Chương trình: {program_display}')
        if self.th_production_office_id:
            filter_info.append(f'Phòng sản xuất: {self.th_production_office_id.name}')
        if self.th_responsible_user_id:
            filter_info.append(f'Người phụ trách: {self.th_responsible_user_id.name}')

        if filter_info:
            worksheet.write(2, 0, f'Bộ lọc: {" | ".join(filter_info)}', data_format)

        # Viết headers
        header_row = 4
        for col, header in enumerate(headers):
            worksheet.write(header_row, col, header, header_format)
            worksheet.set_column(col, col, 25)  # Set column width

        # Viết dữ liệu
        for row, line in enumerate(self.th_report_line_ids, header_row + 1):
            worksheet.write(row, 0, line.th_group_name or '', data_format)
            worksheet.write(row, 1, line.th_program_type_display or '', data_format)
            worksheet.write(row, 2, line.th_production_office or '', data_format)
            worksheet.write(row, 3, line.th_responsible_user or '', data_format)
            worksheet.write(row, 4, line.th_course_count, number_format)

        # Viết tổng kết
        total_row = header_row + len(self.th_report_line_ids) + 1
        worksheet.write(total_row, 3, 'TỔNG CỘNG:', header_format)
        worksheet.write(total_row, 4, self.th_total_courses, number_format)

        workbook.close()
        excel_data = output.getvalue()

        # Tạo attachment để download
        filename = f'bao_cao_thong_ke_khoa_hoc_{datetime.now().strftime("%Y%m%d_%H%M%S")}.xlsx'
        attachment = self.env['ir.attachment'].create({
            'name': filename,
            'type': 'binary',
            'datas': base64.b64encode(excel_data),
            'res_model': 'th.course.report.wizard',
            'res_id': self.id,
            'mimetype': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
        })

        return {
            'type': 'ir.actions.act_url',
            'url': f'/web/content/{attachment.id}?download=true',
            'target': 'self',
        }


class ThCourseReportLine(models.TransientModel):
    _name = 'th.course.report.line'
    _description = 'Dòng báo cáo thống kê khóa học'

    th_wizard_id = fields.Many2one(
        'th.course.report.wizard',
        string='Wizard',
        ondelete='cascade'
    )

    th_group_name = fields.Char(string='Tên nhóm', required=True)

    # Trường cho hiển thị text
    th_program_type_display = fields.Char(string='Loại chương trình')
    th_production_office = fields.Char(string='Phòng sản xuất')
    th_responsible_user = fields.Char(string='Người phụ trách')

    # Trường Many2one và Selection cho pivot/graph view
    th_program_type = fields.Selection([
        ('short', 'Ngắn hạn'),
        ('long', 'Dài hạn'),
        ('mixed', 'Hỗn hợp'),
        ('undefined', 'Chưa xác định')
    ], string='Loại chương trình (Pivot)')

    th_production_office_id = fields.Many2one(
        'th.office.production',
        string='Phòng sản xuất (Pivot)'
    )

    th_responsible_user_id = fields.Many2one(
        'res.users',
        string='Người phụ trách (Pivot)'
    )

    th_course_count = fields.Integer(string='Số lượng khóa học')

    th_course_ids = fields.Many2many(
        'th.course.lpm2',
        string='Danh sách khóa học',
        help='Danh sách các khóa học trong nhóm này'
    )

    def th_action_view_courses(self):
        """Xem danh sách khóa học trong nhóm"""
        return {
            'type': 'ir.actions.act_window',
            'name': _('Khóa học - %s') % self.th_group_name,
            'res_model': 'th.course.lpm2',
            'view_mode': 'tree,form',
            'domain': [('id', 'in', self.th_course_ids.ids)],
            'context': {'create': False}
        }

    def th_export_excel_from_pivot(self):
        """Xuất Excel từ pivot view"""
        # Lấy tất cả records cùng wizard
        all_lines = self.search([('th_wizard_id', '=', self.th_wizard_id.id)])

        if not all_lines:
            raise ValidationError(_('Không có dữ liệu để xuất Excel'))

        # Tạo file Excel
        output = BytesIO()
        workbook = xlsxwriter.Workbook(output)
        worksheet = workbook.add_worksheet('Phân tích thống kê khóa học')

        # Định dạng
        header_format = workbook.add_format({
            'bold': True,
            'bg_color': '#D7E4BC',
            'border': 1,
            'align': 'center',
            'valign': 'vcenter'
        })

        data_format = workbook.add_format({
            'border': 1,
            'align': 'left',
            'valign': 'vcenter'
        })

        number_format = workbook.add_format({
            'border': 1,
            'align': 'center',
            'valign': 'vcenter',
            'num_format': '#,##0'
        })

        # Headers
        headers = [
            'Tên nhóm',
            'Loại chương trình',
            'Phòng sản xuất',
            'Người phụ trách',
            'Số lượng khóa học',
        ]

        # Viết tiêu đề
        worksheet.merge_range('A1:E1', 'PHÂN TÍCH THỐNG KÊ KHÓA HỌC', header_format)
        worksheet.write(1, 0, f'Ngày tạo: {datetime.now().strftime("%d/%m/%Y %H:%M:%S")}', data_format)

        # Thông tin bộ lọc từ context
        if hasattr(self, '_context') and self._context.get('th_filter_info'):
            worksheet.write(2, 0, f'Bộ lọc: {self._context.get("th_filter_info")}', data_format)

        # Viết headers
        header_row = 4
        for col, header in enumerate(headers):
            worksheet.write(header_row, col, header, header_format)
            worksheet.set_column(col, col, 25)

        # Viết dữ liệu
        for row, line in enumerate(all_lines, header_row + 1):
            worksheet.write(row, 0, line.th_group_name or '', data_format)
            worksheet.write(row, 1, line.th_program_type_display or '', data_format)
            worksheet.write(row, 2, line.th_production_office or '', data_format)
            worksheet.write(row, 3, line.th_responsible_user or '', data_format)
            worksheet.write(row, 4, line.th_course_count, number_format)

        # Tổng kết
        total_row = header_row + len(all_lines) + 1
        total_courses = sum(all_lines.mapped('th_course_count'))
        worksheet.write(total_row, 3, 'TỔNG CỘNG:', header_format)
        worksheet.write(total_row, 4, total_courses, number_format)

        workbook.close()
        excel_data = output.getvalue()

        # Tạo attachment
        filename = f'phan_tich_thong_ke_khoa_hoc_{datetime.now().strftime("%Y%m%d_%H%M%S")}.xlsx'
        attachment = self.env['ir.attachment'].create({
            'name': filename,
            'type': 'binary',
            'datas': base64.b64encode(excel_data),
            'res_model': 'th.course.report.line',
            'res_id': self.id,
            'mimetype': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
        })

        return {
            'type': 'ir.actions.act_url',
            'url': f'/web/content/{attachment.id}?download=true',
            'target': 'self',
        }
