<odoo>
    <record id="apm_lead_b2b_view_form" model="ir.ui.view">
        <field name="name">apm_lead_view_form</field>
        <field name="model">th.apm</field>
        <field name="arch" type="xml">
            <form string="">
                <header>
                    <field name="create_date" invisible="1"/>
                    <field name="th_last_status_stage" invisible="1"/>
                    <field name="th_sale_order_count" invisible="1"/>
                    <field name="th_order_created" invisible="1"/>
                    <button name="th_action_hand_over_apm" string="Bàn giao Cơ hội" type="object" class="oe_highlight" title="Bàn giao Cơ hội" attrs="{'invisible': [('is_handed_over', '=', True)]}"
                           />
                     <field name="is_handed_over"  invisible="1"/>
                    <field name="th_stage_id" widget="th_statusbar" class="o_field_statusbar"
                           options="{'clickable':'1'}"
                            attrs="{'invisible': [('is_handed_over', '=', True)]}" domain="[('is_level_partner', '=', True)]"/>

                   <field name="th_stage_b2b_id" widget="th_statusbar" class="o_field_statusbar"
                                   domain="th_stage_id_domain"  attrs="{'invisible': [('is_handed_over', '=', False)]}"/>
                    <button name="action_stop_caring" string="Dừng chăm sóc" type="object" class="oe_highlight"
                            attrs="{'invisible': ['|','|','|','|', ('th_last_status_stage', '=', True), ('th_reason', '!=', False),('is_handed_over', '=', True), ('create_date', '=', False),('is_handed_over', '!=', True)]}"/>

                </header>

                <sheet>

                    <field name="readonly_domain" invisible="1"/>

                    <div name="button_box" position="inside">

<!--                        <field name="purchase_order_count" invisible="1"/>-->
<!--                        <button class="oe_stat_button" type="object" name="action_show_refund_purchase_apm" icon="fa-ban" attrs="{'invisible': [('purchase_order_count', '=', 0)]}">-->
<!--                            <field string="Đơn hoàn trả" name="purchase_order_count" widget="statinfo"/>-->
<!--                        </button>-->
                        <button class="oe_stat_button" string="Đơn hàng" type="object" name="action_view_sale_order_b2b"
                                icon="fa-pencil-square-o"
                                attrs="{'invisible': ['|','|',('th_last_status_stage', '!=', True), ('th_sale_order_count', '!=', True),('th_reason', '!=', False),  ('th_order_created', '!=', True)]}">
                            <div class="o_stat_info">
                                <field name="sale_order_count" readonly="1" class="o_stat_value"/>
                                <span class="">
                                    Đơn hàng
                                </span>
                            </div>
                        </button>
                        <button name="action_open_apm_partner_b2b" type="object" class="oe_stat_button"
                                icon="fa-address-card"
                                attrs="{'invisible' :[('th_partner_id', '=', False)]}">
                            <div class="o_stat_info">
                                <span class="o_stat_text">
                                    Khách hàng
                                </span>
                            </div>
                        </button>
                    </div>
                    <widget name="web_ribbon" title="Đã bàn giao" bg_color="bg-danger" attrs="{'invisible': [('is_handed_over', '!=', True)]}"/>
                    <widget name="web_ribbon" title="Dừng chăm sóc" bg_color="bg-danger"
                            attrs="{'invisible': [('th_reason', '=', False)]}"/>
                    <div name="button_box" position="inside" invisible="1">
                        <button class="oe_stat_button" type="object" name="action_view_sale_order_b2b" icon="fa-usd">
                            <field string="Sales" name="sale_order_count" widget="statinfo"/>
                        </button>
                    </div>
                    <div class="oe_title">
                        <h1>
                            <field class="text-break" name="name" placeholder="Tên cơ hội" readonly="1"/>
                        </h1>
                    </div>
                    <h4 class="d-flex gap-2 g-0 pb-3">
                        <div style="width: 15%; min-width: 4rem;">
                            <label for="th_last_check"/>
                            <br/>
                            <field name="th_last_check" widget="remaining_days" readonly="1"/>
                        </div>
                    </h4>
                    <group>
                        <group>
                            <field name="th_customer_code" readonly="1"/>
                            <field name="th_customer_code_aum" invisible="1"/>
                            <field name="th_partner_id"
                                   options='{"no_open": True, "no_quick_create": True}' required="1"
                                   attrs="{'readonly': [('id', '!=', False)]}"
                                   context="{'default_phone': th_check_phone, 'default_email': th_check_email, 'default_th_partner_email': th_check_email, 'apm_contact': True}"
                                   string="Khách hàng*"/>
                            <field name="name_id_sequence" invisible="1"/>
                            <!--                            <field name="th_source_id"/>-->
                            <!--                            <field name="th_campaign_domain"/>-->
                            <field name="th_after_sales_care" invisible="1"/>
                            <field name="th_stage_id_domain" invisible="1"/>
                            <field name="th_partner_phone" attrs="{'invisible': [('th_partner_id', '=', False)]}"/>
                            <field name="th_partner_email" string="Email" attrs="{'invisible': [('th_partner_id', '=', False)]}"/>
                            <field name="th_check_phone" attrs="{'invisible': [('th_partner_id', '!=', False)]}"/>
                            <field name="th_check_email" string="Email" attrs="{'invisible': [('th_partner_id', '!=', False)]}"/>
                            <field name="th_ownership_unit_id" options="{'no_create': True, 'no_open':True}"
                                   attrs="{'readonly': [('th_reason', '!=', False)]}" required="1"
                                   string="Đơn vị sở hữu*"/>
                            <field name="th_partner_reference_id" options="{'no_create': True, 'no_open':True}"
                                   attrs="{'readonly': ['|',('th_reason', '!=', False),('th_check_partner_referred', '=', True)]}"/>
                            <field name="th_check_partner_referred" invisible="1"/>
                            <field name="th_status_category_id" options="{'no_create': True, 'no_open':True}"
                                   string="Nhóm trạng thái*" attrs="{'readonly': [('th_reason', '!=', False)]}" domain="th_status_category_domain"/>
                            <field name="th_status_detail_id" options="{'no_create': True, 'no_open':True}"
                                   domain="th_state_detail_domain"
                                   attrs="{'invisible': [('th_status_category_id', '=', False),('th_reason', '!=', False)],'readonly': [('th_reason', '!=', False)]}"
                                   string="Trạng thái chi tiết*"/>
                             <field name="th_processing_status_id" attrs="{'readonly': [('th_reason', '!=', False)], 'invisible': ['|',('th_status_detail_id', '=', False),('th_is_lead_TTVH', '!=', True)]}" options="{'no_create': True, 'no_open':True}"/>
                            <field name="th_state_detail_domain" invisible="1"/>
                            <field name="th_status_category_domain" invisible="1"/>
                            <field name="th_level_up_date"/>
                        </group>
                        <group>
                            <field name="th_check_group_admin_domain" invisible="1"/>
                            <field name="th_campaign_id" options="{'no_create': True, 'no_open':True}"
                                   attrs="{'readonly': ['|', ('th_reason', '!=', False),('th_check_group_admin_domain', '!=', True)]}"
                                   required="1" string="Chiến dịch*"/>
                            <field name="th_origin_id" options="{'no_create': True, 'no_open':True}" readonly="1"
                                   force_save="1" string="Dòng sản phẩm"
                                   attrs="{'invisible': [('th_is_lead_TTVH', '=', True)]}"/>
                            <field name="th_origin_ids" domain="[('th_module_ids.name', 'in', ['APM'])]" options="{'no_create': True, 'no_open':True}"
                                   widget="many2many_tags" force_save="1" string="Dòng sản phẩm*"
                                   attrs="{'invisible': [('th_is_lead_TTVH', '=', False)]}"/>
                            <field name="th_product_category_id" domain="th_product_category_domain"
                                   options="{'no_create': True, 'no_open':True}"
                                   attrs="{'readonly': [('th_product_category_id', '!=', False)]}" force_save="1"
                                   invisible="1"/>
                            <field name="th_product_categorys_domain" invisible="1"/>
                            <field name="th_product_category_ids" string="Nhóm sản phẩm" options="{'no_create': True, 'no_open':True}" widget="many2many_tags"
                                   domain="th_product_categorys_domain"/>
                            <field name="th_product_ids" widget="many2many_tags"
                                   options="{'no_create': True, 'no_open':True}" domain="th_product_domain"
                                   attrs="{'readonly': [('th_reason', '!=', False)]}"/>
                            <!--                            <field name="th_need_id" options="{'no_quick_create': True}"/>-->
                            <field name="th_apm_b2b_dividing_ring_id" string="Vòng chia" options="{'no_create': True, 'no_open':True}" attrs="{'readonly': [('th_reason', '!=', False)]}"/>
                            <field name="th_apm_dividing_ring_id" invisible="1"/>
                            <field name="th_apm_dividing_ring_domain" invisible="1"/>
                            <field name="th_apm_b2b_dividing_ring_domain" invisible="1"/>
                            <field name="th_reason" options='{"no_open": True, "no_create": True}' readonly="1"
                                   attrs="{'invisible': [('th_reason', '=', False)]}"/>
                            <field name="th_user_id_domain" invisible="1"/>
                            <field name="th_apm_team_domain" invisible="1"/>
<!--                            <field name="th_apm_team_id" options="{'no_create': True, 'no_open':True}"-->
<!--                                   domain="th_apm_team_domain" attrs="{'readonly': [('th_reason', '!=', False)]}"/>-->

                            <field name="th_user_id" options="{'no_create': True, 'no_open':True}"
                                   domain="th_user_id_domain" attrs="{'readonly': [('th_reason', '!=', False)]}"/>
                            <field name="th_product_category_domain" invisible="1"/>
                            <field name="th_product_domain" invisible="1"/>
                            <field name="th_source_group_id" options="{'no_open': True, 'no_create': True,'no_edit': True}"
                                   attrs="{'readonly': [('th_reason', '!=', False)], 'invisible': [('th_is_lead_TTVH', '=', True)], 'required': [('th_is_lead_TTVH', '=', False)]}"
                                   string="Nhóm nguồn*" />
                            <field name="th_source_name" attrs="{ 'invisible': [('th_after_sales_care', '=', True)]}"/>
                            <field name="th_channel_id"
                                   attrs="{'readonly': [('th_reason', '!=', False)], 'invisible': [('th_is_lead_TTVH', '=', True)]}" options="{'no_open': True, 'no_create': True,'no_edit': True}"/>
                            <field name="th_ecommerce_platform" attrs="{'readonly': [('id', '!=', False)], 'invisible': [('th_after_sales_care', '=', True)]}"/>
                            <field name="th_is_lead_TTVH" invisible="1"/>
                            <field name="th_campaign_domain" invisible="1"/>
                            <field name="th_resign_id" attrs="{'readonly': [('th_reason', '!=', False)], 'invisible': [('th_is_lead_TTVH', '!=', True)]}" options="{'no_create': True, 'no_open':True}"/>
                            <field name="th_detail_resign_id" attrs="{'readonly': [('th_reason', '!=', False)], 'invisible': ['|',('th_resign_id', '=', False),('th_is_lead_TTVH', '!=', True)]}" options="{'no_create': True, 'no_open':True}"/>
                            <field name="th_day_recent_purchase" readonly="True" attrs="{ 'invisible': [('th_is_lead_TTVH', '!=', True)]}"/>
                        </group>
                    </group>
                    <notebook>
                        <page string="Mô tả">
                            <field name="th_description" placeholder="Thêm mô tả..."/>
                        </page>
                        <page string="Đặc điểm">
                            <field name="th_apm_lead_trait_ids">
                                <tree editable="bottom" no_open="1">
                                    <!--                                    <field name="th_product_line_id" domain="th_product_line_domain"/>-->
                                    <field name="th_origin_id" domain="th_product_line_domain" options="{'no_create': True, 'no_open':True}"/>
                                    <field name="th_apm_trait_id" domain="th_trait_domain" options="{'no_create': True, 'no_open':True}"/>
                                    <field name="th_apm_trait_value_ids" widget="many2many_tags"
                                           domain="th_trait_value_domain" options="{'no_create': True, 'no_open':True}"/>
                                    <field name="th_trait_domain" invisible="1"/>
                                    <field name="th_trait_value_domain" invisible="1"/>
                                    <field name="th_apm_contact_trait_id" invisible="1"/>
                                    <field name="th_product_line_domain" invisible="1"/>
                                </tree>
                            </field>
                        </page>
                        <page string="Dữ liệu Getfly" attrs="{'invisible': [('th_data_getfly', '=', False)]}">
                            <field name="th_data_getfly" readonly="1"/>
                        </page>
                        <page string="Cơ hội">
                            <field name="th_opportunity_list_partner_ids">
                                <tree no_open="1">
                                    <field name="name"/>
                                    <field name="th_origin_id"/>
                                    <field name="th_last_check"/>
                                    <field name="th_campaign_id"/>
                                    <field name="th_stage_id"/>
                                    <field name="th_ownership_unit_id"/>
                                </tree>
                            </field>
                        </page>
                        <page string="Lịch sử mua hàng" attrs="{'invisible': [('th_is_lead_TTVH', '=', False)]}">
                            <field name="th_order_history_ids">
                                <tree create="0" edit="0" delete="0" no_open="1">
                                    <field name="th_sale_order_id"/>
                                    <field name="th_origin_id" />
                                    <field name="th_product_ids" widget="many2many_tags"/>
                                    <field name="th_create_date"/>
                                    <field name="th_ownership_unit_id"/>
                                    <field name="user_id"/>
                                </tree>
                            </field>
                        </page>
                         <page string="Tình trạng học viên"  attrs="{'invisible': [('th_is_lead_TTVH', '=', False)]}">
                             <field name="th_check_c" invisible="1"/>
                               <button name="action_open_import_student_status" string="Import" type="object" class="btn-primary"/>
                               <button name="action_export_data" string="Export" type="object" class="btn-primary"/>
                            <field name="th_student_status_ids"  attrs="{'readonly': [('th_reason', '!=', False)]}">
                                <tree editable="bottom" create="0" delete="0" no_open="1" >
                                    <field name="th_origin_ids" string="Dòng sản phẩm" widget="many2many_tags"/>
                                    <field name="th_sale_order_id" readonly="1" options="{'no_open': True}"/>
                                    <field name="th_ownership_unit_id" readonly="1" />
                                    <field name="th_product_id" readonly="1" options="{'no_open': True}"/>
                                    <field name="th_level" options='{"no_open": True, "no_create": True}'/>
                                    <field name="th_registration_date"/>
                                    <field name="th_activation_date" />
                                    <field name="th_closing_date" readonly="True"/>
                                    <field name="th_renewal_date"/>
                                    <field name="th_extension_end_date" readonly="True"/>
                                </tree>
                            </field>
                        </page>
                        <page string="Thông tin giới thiệu" attrs="{'invisible': [('th_utm_source', '=', False),('th_utm_medium', '=', False),('th_utm_campaign', '=', False),('th_utm_term', '=', False),('th_utm_content', '=', False)]}">
                            <group>
                                <field name="th_utm_source" readonly="1"/>
                                <field name="th_utm_medium" readonly="1"/>
                                <field name="th_utm_campaign" readonly="1"/>
                                <field name="th_utm_term" readonly="1"/>
                                <field name="th_utm_content" readonly="1"/>
                            </group>
                        </page>
                    </notebook>
                </sheet>
                <div class="oe_chatter">
                    <field name="message_follower_ids"/>
                    <field name="activity_ids"/>
                    <field name="message_ids"/>
                </div>
            </form>
        </field>
    </record>

    <record id="apm_lead_b2b_view_tree" model="ir.ui.view">
        <field name="name">apm_lead_view_tree</field>
        <field name="model">th.apm</field>
        <field name="arch" type="xml">
            <tree string="" default_order="th_last_check desc">
                <field name="th_customer_code" widget="tree_url" />
                <field name="th_customer_code_aum" widget="tree_url" invisible="1"/>
                <field name="th_last_check" widget="remaining_days" optional="show" readonly="1"/>
                <field name="th_partner_id"/>
                <field name="th_partner_phone"/>
                <field name="th_partner_email" string="Email"/>
                <field name="th_origin_id"/>
                <field name="th_product_ids" widget="many2many_tags"/>
                <field name="th_owner_history_ids" widget="many2many_tags" invisible="1"/>
                <field name="th_user_ownership_ids" widget="many2many_tags" invisible="1"/>
                <field name="th_user_id"/>
                <field name="th_status_detail_id"/>
                <field name="th_stage_id"/>
                <field name="th_partner_reference_id"/>
                <field name="th_ownership_unit_id"/>
                <field name="th_campaign_id"/>
                <field name="th_status_payment"/>
            </tree>
        </field>
    </record>

    <record id="apm_after_sale_b2b_view_form" model="ir.ui.view">
        <field name="name">apm_lead_view_form</field>
        <field name="model">th.apm</field>
        <field name="arch" type="xml">
            <form string="">
                <header>
                    <field name="create_date" invisible="1"/>
                    <field name="th_last_status_stage" invisible="1"/>
                    <field name="th_sale_order_count" invisible="1"/>
                    <field name="th_order_created" invisible="1"/>
<!--                    <button name="action_create_order" string="Tạo đơn hàng" type="object" class="oe_highlight"-->
<!--                            attrs="{'invisible': ['|', ('th_last_status_stage', '!=', True), ('th_reason', '!=', False)]}"/>-->
<!--                    <field name="th_stage_id" widget="th_statusbar" class="o_field_statusbar"-->
<!--                           options="{'clickable':'1'}" domain="th_stage_id_domain"/>-->
                    <field name="th_stage_id" widget="th_statusbar" class="o_field_statusbar"
                           domain="th_stage_id_domain"/>
                    <button name="action_stop_caring" string="Dừng chăm sóc" type="object" class="oe_highlight"
                            attrs="{'invisible': ['|','|', ('th_last_status_stage', '=', True), ('th_reason', '!=', False), ('create_date', '=', False)]}"/>
<!--                    <button name="compute_order_history" string="Cập nhật lịch sử mua hàng" type="object"-->
<!--                            class="oe_highlight" attrs="{'invisible': [('th_is_lead_TTVH', '=', False)]}"/>-->
                </header>
                <sheet>
                     <field name="readonly_domain" invisible="1"/>
                    <div name="button_box" position="inside">
<!--                        <field name="purchase_order_count" invisible="1"/>-->
<!--                        <button class="oe_stat_button" type="object" name="action_show_refund_purchase_apm" icon="fa-ban" attrs="{'invisible': [('purchase_order_count', '=', 0)]}">-->
<!--                            <field string="Đơn hoàn trả" name="purchase_order_count" widget="statinfo"/>-->
<!--                        </button>-->
                        <button class="oe_stat_button" string="Đơn hàng" type="object" name="action_view_sale_order_b2b"
                                icon="fa-pencil-square-o"
                                attrs="{'invisible': ['|','|',('th_last_status_stage', '!=', True), ('th_sale_order_count', '!=', True),('th_reason', '!=', False),  ('th_order_created', '!=', True)]}">
                            <div class="o_stat_info">
                                <field name="sale_order_count" readonly="1" class="o_stat_value"/>
                                <span class="">
                                    Đơn hàng
                                </span>
                            </div>
                        </button>
                        <button name="action_open_apm_partner_b2b" type="object" class="oe_stat_button"
                                icon="fa-address-card"
                                attrs="{'invisible' :[('th_partner_id', '=', False)]}">
                            <div class="o_stat_info">
                                <span class="o_stat_text">
                                    Khách hàng
                                </span>
                            </div>
                        </button>
                    </div>
                    <widget name="web_ribbon" title="Dừng chăm sóc" bg_color="bg-danger"
                            attrs="{'invisible': [('th_reason', '=', False)]}"/>
                    <div name="button_box" position="inside" invisible="1">
                        <button class="oe_stat_button" type="object" name="action_view_sale_order" icon="fa-usd">
                            <field string="Sales" name="sale_order_count" widget="statinfo"/>
                        </button>
                    </div>
                    <div class="oe_title">
                        <h1>
                            <field class="text-break" name="name" placeholder="Tên cơ hội" readonly="1"/>
                        </h1>
                    </div>
                    <h4 class="d-flex gap-2 g-0 pb-3">
                        <div style="width: 15%; min-width: 4rem;">
                            <label for="th_last_check"/>
                            <br/>
                            <field name="th_last_check" widget="remaining_days" readonly="1"/>
                        </div>
                    </h4>
                    <group>
                        <group>
                            <field name="th_customer_code" readonly="1"/>
                            <field name="th_customer_code_aum" invisible="1"/>
                            <field name="th_partner_id"
                                   options='{"no_open": True, "no_quick_create": True}' required="1"
                                   attrs="{'readonly': [('id', '!=', False)]}"
                                   context="{'default_phone': th_check_phone, 'default_email': th_check_email, 'default_th_partner_email': th_check_email, 'apm_contact': True}"
                                   string="Khách hàng*"/>
                            <field name="name_id_sequence" invisible="1"/>
                            <!--                            <field name="th_source_id"/>-->
                            <!--                            <field name="th_campaign_domain"/>-->
                            <field name="th_after_sales_care" invisible="1"/>

                            <field name="th_stage_id_domain" invisible="1"/>
                            <field name="th_partner_phone" attrs="{'invisible': [('th_partner_id', '=', False)]}"/>
                            <field name="th_partner_email" string="Email" attrs="{'invisible': [('th_partner_id', '=', False)]}"/>
                            <field name="th_check_phone" attrs="{'invisible': [('th_partner_id', '!=', False)]}"/>
                            <field name="th_check_email" string="Email" attrs="{'invisible': [('th_partner_id', '!=', False)]}"/>
                            <field name="th_ownership_unit_id" options="{'no_create': True, 'no_open':True}"
                                   attrs="{'readonly': [('th_reason', '!=', False)]}" required="1"
                                   string="Đơn vị sở hữu*"/>
                            <field name="th_owner_history_ids" widget="many2many_tags" invisible="1"/>
                            <field name="th_user_ownership_ids" widget="many2many_tags" invisible="1"/>
                            <field name="th_partner_reference_id" options="{'no_create': True, 'no_open':True}"
                                   attrs="{'readonly': ['|',('th_reason', '!=', False),('th_check_partner_referred', '=', True)]}"/>
                            <field name="th_check_partner_referred" invisible="1"/>
                            <field name="th_status_category_id" options="{'no_create': True, 'no_open':True}"
                                   string="Nhóm trạng thái*" attrs="{'readonly': [('th_reason', '!=', False)]}" domain="th_status_category_domain"/>
                            <field name="th_status_detail_id" options="{'no_create': True, 'no_open':True}"
                                   domain="th_state_detail_domain"
                                   attrs="{'invisible': [('th_status_category_id', '=', False),('th_reason', '!=', False)],'readonly': [('th_reason', '!=', False)]}"
                                   string="Trạng thái chi tiết*"/>
                             <field name="th_processing_status_id" attrs="{'readonly': [('th_reason', '!=', False)], 'invisible': ['|',('th_status_detail_id', '=', False),('th_is_lead_TTVH', '!=', True)]}" options="{'no_create': True, 'no_open':True}"/>
                            <field name="th_state_detail_domain" invisible="1"/>
                            <field name="th_status_category_domain" invisible="1"/>
                            <field name="th_level_up_date"/>
                        </group>
                        <group>
                            <field name="th_check_group_admin_domain" invisible="1"/>
                            <field name="th_campaign_id" options="{'no_create': True, 'no_open':True}"
                                   attrs="{'readonly': ['|', ('th_reason', '!=', False),('th_check_group_admin_domain', '!=', True)]}"
                                   required="1" string="Chiến dịch*"/>
                            <field name="th_origin_id" options="{'no_create': True, 'no_open':True}" readonly="1"
                                   force_save="1" string="Dòng sản phẩm"
                                   attrs="{'invisible': [('th_is_lead_TTVH', '=', True)]}"/>
                            <field name="th_origin_ids" domain="[('th_module_ids.name', 'in', ['APM'])]" options="{'no_create': True, 'no_open':True}"
                                   widget="many2many_tags" force_save="1" string="Dòng sản phẩm*"
                                   attrs="{'invisible': [('th_is_lead_TTVH', '=', False)]}"/>
                            <field name="th_product_categorys_domain" invisible="1"/>
                            <field name="th_product_category_ids" string="Nhóm sản phẩm" options="{'no_create': True, 'no_open':True}" widget="many2many_tags"
                                   domain="th_product_categorys_domain"/>
                            <field name="th_product_category_id" domain="th_product_category_domain"
                                   options="{'no_create': True, 'no_open':True}"
                                   attrs="{'readonly': [('th_product_category_id', '!=', False)]}" force_save="1"
                                   invisible="1"/>
                            <field name="th_product_ids" widget="many2many_tags"
                                   options="{'no_create': True, 'no_open':True}" domain="th_product_domain"
                                   attrs="{'readonly': [('th_reason', '!=', False)]}"/>
                            <!--                            <field name="th_need_id" options="{'no_quick_create': True}"/>-->
                            <field name="th_apm_dividing_ring_id" options="{'no_create': True, 'no_open':True}" attrs="{'readonly': [('th_reason', '!=', False)]}"/>
                            <field name="th_apm_dividing_ring_domain" invisible="1"/>
                            <field name="th_reason" options='{"no_open": True, "no_create": True}' readonly="1"
                                   attrs="{'invisible': [('th_reason', '=', False)]}"/>
                            <field name="th_user_id_domain" invisible="1"/>
                            <field name="th_apm_team_domain" invisible="1"/>
<!--                            <field name="th_apm_team_id" options="{'no_create': True, 'no_open':True}"-->
<!--                                   domain="th_apm_team_domain" attrs="{'readonly': [('th_reason', '!=', False)]}"/>-->
                            <field name="th_user_id" options="{'no_create': True, 'no_open':True}"
                                   domain="th_user_id_domain" attrs="{'readonly': [('th_reason', '!=', False)]}"/>
                            <field name="th_product_category_domain" invisible="1"/>
                            <field name="th_product_domain" invisible="1"/>
                            <field name="th_source_group_id" options="{'no_open': False, 'no_create': False,'no_edit': True}"
                                   attrs="{'readonly': [('th_reason', '!=', False)], 'invisible': [('th_is_lead_TTVH', '=', True)], 'required': [('th_is_lead_TTVH', '=', False)]}"
                                   string="Nhóm nguồn*"/>
                            <field name="th_source_name" attrs="{ 'readonly': [('th_reason', '!=', False)]}"/>
                            <field name="th_channel_id"
                                   attrs="{'readonly': [('th_reason', '!=', False)], 'invisible': [('th_is_lead_TTVH', '=', True)]}" options="{'no_open': False, 'no_create': False,'no_edit': True}"/>
                            <field name="th_is_lead_TTVH" invisible="1"/>
                            <field name="th_campaign_domain" invisible="1"/>
                            <field name="th_resign_id" attrs="{'readonly': [('th_reason', '!=', False)], 'invisible': [('th_is_lead_TTVH', '!=', True)]}" options="{'no_create': True, 'no_open':True}"/>
                            <field name="th_detail_resign_id" attrs="{'readonly': [('th_reason', '!=', False)], 'invisible': ['|',('th_resign_id', '=', False),('th_is_lead_TTVH', '!=', True)]}" options="{'no_create': True, 'no_open':True}"/>
                            <field name="th_day_recent_purchase" readonly="True" attrs="{ 'invisible': [('th_is_lead_TTVH', '!=', True)]}"/>
                        </group>
                    </group>
                    <notebook>
                        <page string="Mô tả">
                            <field name="th_description" placeholder="Thêm mô tả..."/>
                        </page>
                        <page string="Đặc điểm">
                            <field name="th_apm_lead_trait_ids" >
                                <tree editable="bottom" no_open="1">
                                    <!--                                    <field name="th_product_line_id" domain="th_product_line_domain"/>-->
                                    <field name="th_origin_id" domain="th_product_line_domain" options="{'no_open': False, 'no_create': False,'no_edit': True}"/>
                                    <field name="th_apm_trait_id" domain="th_trait_domain"/>
                                    <field name="th_apm_trait_value_ids" widget="many2many_tags"
                                           domain="th_trait_value_domain"/>
                                    <field name="th_trait_domain" invisible="1"/>
                                    <field name="th_trait_value_domain" invisible="1"/>
                                    <field name="th_apm_contact_trait_id" invisible="1"/>
                                    <field name="th_product_line_domain" invisible="0"/>
                                </tree>
                            </field>
                        </page>
                        <page string="Dữ liệu Getfly" attrs="{'invisible': [('th_data_getfly', '=', False)]}">
                            <field name="th_data_getfly" readonly="1"/>
                        </page>
                        <page string="Cơ hội">
                            <field name="th_opportunity_list_partner_ids">
                                <tree no_open="1">
                                    <field name="name"/>
                                    <field name="th_origin_id"/>
                                    <field name="th_last_check"/>
                                    <field name="th_campaign_id"/>
                                    <field name="th_stage_id"/>
                                    <field name="th_ownership_unit_id"/>
                                </tree>
                            </field>
                        </page>
                        <page string="Lịch sử mua hàng" attrs="{'invisible': [('th_is_lead_TTVH', '=', False)]}">
                            <field name="th_order_history_ids">
                                <tree create="0" edit="0" delete="0" no_open="1">
                                    <field name="th_sale_order_id"/>
                                    <field name="th_origin_id"/>
                                    <field name="th_product_ids" widget="many2many_tags"/>
                                    <field name="th_create_date"/>
                                    <field name="th_ownership_unit_id"/>
                                    <field name="th_ownership_unit_new_id"/>
                                    <field name="user_id"/>
                                </tree>
                            </field>
                        </page>
                         <page string="Tình trạng học viên"  attrs="{'invisible': [('th_is_lead_TTVH', '=', False)]}">
                             <field name="th_check_c" invisible="1"/>
                            <field name="th_student_status_ids"  attrs="{'readonly': [('th_reason', '!=', False)]}">
                                <tree editable="bottom" create="0" delete="0" no_open="1" >
                                    <field name="th_origin_ids" string="Dòng sản phẩm" widget="many2many_tags"/>
                                    <field name="th_sale_order_id" readonly="1" options="{'no_open': True}"/>
                                    <field name="th_ownership_unit_id" readonly="1" />
                                    <field name="th_product_id" readonly="1" options="{'no_open': True}"/>
                                    <field name="th_level" options='{"no_open": True, "no_create": True}'/>
                                    <field name="th_registration_date"/>
                                    <field name="th_activation_date" />
                                    <field name="th_closing_date" readonly="True"/>
                                    <field name="th_renewal_date"/>
                                    <field name="th_extension_end_date" readonly="True"/>
                                </tree>
                            </field>
                        </page>
                        <page string="Thông tin giới thiệu" attrs="{'invisible': [('th_utm_source', '=', False),('th_utm_medium', '=', False),('th_utm_campaign', '=', False),('th_utm_term', '=', False),('th_utm_content', '=', False)]}">
                            <group>
                                <field name="th_utm_source" readonly="1"/>
                                <field name="th_utm_medium" readonly="1"/>
                                <field name="th_utm_campaign" readonly="1"/>
                                <field name="th_utm_term" readonly="1"/>
                                <field name="th_utm_content" readonly="1"/>
                            </group>
                        </page>
                    </notebook>
                </sheet>
                <div class="oe_chatter">
                    <field name="message_follower_ids"/>
                    <field name="activity_ids"/>
                    <field name="message_ids"/>
                </div>
            </form>
        </field>
    </record>

    <record id="th_apm_b2b_popup_view_form" model="ir.ui.view">
        <field name="name">th_apm_popup_view_form</field>
        <field name="model">th.apm</field>
        <field name="arch" type="xml">
            <form string="">
                <sheet>
                    <group>
                        <!-- <field name="th_apm_team_id" required="1" options="{'no_create': 1, 'no_open':1}"/> -->
                        <field name="th_apm_b2b_dividing_ring_id" string="Vòng chia" options="{'no_create': 1, 'no_open':1}"/>
                        <field name="th_apm_b2b_dividing_ring_domain" invisible="1"/>
                        <field name="th_apm_dividing_ring_id" invisible="1"/>
                        <field name="th_apm_dividing_ring_domain" invisible="1"/>
                        <field name="th_user_id" options="{'no_create': 1, 'no_open':1}"/>
                    </group>
                    <footer>
                        <button name="th_accept_team_apm" string="Xác nhận" type="object" class="btn btn-primary"/>
                        <button string="HỦY" class="oe_link" special="cancel"/>
                    </footer>
                </sheet>
            </form>
        </field>
    </record>

    <record id="th_apm_b2b_inherit_view_tree" model="ir.ui.view">
        <field name="name">Cơ hội</field>
        <field name="model">th.apm</field>
        <field name="inherit_id" ref="th_apm.apm_lead_view_tree"/>
        <field name="arch" type="xml">
            <xpath expr="//field[@name='th_product_ids']" position="after">
                <field name="th_owner_history_ids" widget="many2many_tags" invisible="1"/>
                <field name="th_user_ownership_ids" widget="many2many_tags" invisible="1"/>
            </xpath>
        </field>
    </record>

    <record id="th_apm_b2b_inherit_view_form" model="ir.ui.view">
        <field name="name">Cơ hội</field>
        <field name="model">th.apm</field>
        <field name="inherit_id" ref="th_apm.apm_lead_view_form"/>
        <field name="arch" type="xml">
            <xpath expr="//field[@name='th_customer_code']" position="after">
                <field name="th_owner_history_ids" widget="many2many_tags" invisible="1"/>
                <field name="th_user_ownership_ids" widget="many2many_tags" invisible="1"/>
            </xpath>
        </field>
    </record>

    <record id="apm_lead_view_b2b_act" model="ir.actions.act_window">
        <field name="name">Cơ hội</field>
        <field name="type">ir.actions.act_window</field>
        <field name="domain">[('th_after_sales_care', '!=', True)]</field>
        <field name="res_model">th.apm</field>
        <field name="view_mode">tree,form</field>
        <field name="view_ids" eval="[(5, 0, 0),
                (0, 0, {'view_mode': 'tree', 'view_id': ref('apm_lead_b2b_view_tree')}),
                (0, 0, {'view_mode': 'form', 'view_id': ref('apm_lead_b2b_view_form')}),
                ]"/>
    </record>

     <record id="apm_lead_after_sale_b2b_view_act" model="ir.actions.act_window">
        <field name="name">Cơ hội sau bán</field>
        <field name="type">ir.actions.act_window</field>
        <field name="domain">[('th_after_sales_care', '=', True)]</field>
        <field name="res_model">th.apm</field>
        <field name="context">{'create':0,'edit':0,'delete':0, 'is_after_order': True}</field>
        <field name="view_mode">tree,form</field>
        <field name="view_ids" eval="[(5, 0, 0),
                (0, 0, {'view_mode': 'tree', 'view_id': ref('th_apm_b2b.apm_lead_b2b_view_tree')}),
                (0, 0, {'view_mode': 'form', 'view_id': ref('th_apm_b2b.apm_after_sale_b2b_view_form')}),
                ]"/>
    </record>




</odoo>
