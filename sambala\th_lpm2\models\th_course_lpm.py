from odoo import fields, models, api, _
from odoo.exceptions import ValidationError
import re
import json
from odoo.http import request

class ThCourseLpm2(models.Model):
    _name = 'th.course.lpm2'
    _inherit = ["mail.thread", "mail.activity.mixin"]
    _description = 'Khóa học LPM2'
    _rec_name = 'th_project_template_id'

    # các trương thông tin chung của khóa học và học phần
    th_project_template_id = fields.Many2one(comodel_name='product.template',string ="Tên")
    th_course_code = fields.Char(string='Mã')
    th_production_standard = fields.Html(string='Tiêu chuẩn sản xuất', tracking=True)
    th_level_production_id = fields.Many2one('th.level.production', string='Level')

    # các trường thông tin liên quan đến khóa học
    th_solution_line = fields.Char(string='Dòng giải pháp')
    th_subject = fields.Char(string='Bộ môn')
    th_topic = fields.Char(string='Chuyên đề')
    th_production_proposed = fields.Selection([
        ('approved', 'Đã duyệt'),
        ('not_approved', 'Không duyệt'),
        ('pending', 'Chưa xử lí')
    ], string='Khóa học được đề xuất sản xuất')
    th_lecturer_status = fields.Selection([
        ('available', 'Đã có'),
        ('not_available', 'Chưa có')
    ], string='Tình trạng giảng viên')
    th_course_recording = fields.Selection([
        ('approved', 'Đã được duyệt'),
        ('not_approved', 'Chưa được duyệt'),
        ('pending', 'Chưa xử lý')
    ], string='Quay khóa học')
    th_content_production_status = fields.Selection([
        ('approved', 'Đã duyệt'),
        ('not_approved', 'Chưa được duyệt'),
        ('pending', 'Chưa xử lý')
    ], string='Sản xuất nội dung')
    th_editing = fields.Selection([
        ('approved', 'Đã được duyệt'),
        ('not_approved', 'Chưa được duyệt'),
        ('pending', 'Chưa xử lý')
    ], string='Biên tập')
    th_publishing = fields.Selection([
        ('done', 'Đã hoàn thành'),
        ('not_done', 'Chưa hoàn thành'),
        ('pending', 'Chưa xử lý')
    ], string='Xuất bản')
    th_project_id = fields.Many2one('th.project.lpm2', string='Dự án')
    th_stage_ids = fields.One2many('th.course.lpm2.stage', 'th_course_id', string='Tiến độ sản xuất')

    # Add new fields for responsible persons
    th_content_responsible_id = fields.Many2one('res.users', string='Người phụ trách nội dung',)
    th_recording_responsible_id = fields.Many2one('res.users', string='Người phụ trách quay')
    th_editing_responsible_id = fields.Many2one('res.users', string='Người phụ trách biên tập')
    th_publishing_responsible_id = fields.Many2one('res.users', string='Người phụ trách xuất bản')

    # các trường thông tin liên quan đến học phần
    th_aum_code = fields.Char(string='Mã AUM')
    th_credit = fields.Integer(string='Số tín chỉ', tracking=True)
    th_production_start_date = fields.Date(string='Ngày bắt đầu sản xuất')
    th_production_end_date = fields.Date(string='Ngày kết thúc sản xuất')
    th_opening_date = fields.Date(string='Ngày lên môn')
    th_lecturer_id = fields.Many2one('res.partner', string='Giảng viên phụ trách', tracking=True)
    th_lecturer_status = fields.Selection([
        ('available', 'Đã có'),
        ('not_available', 'Chưa có')
    ], string='Tình trạng giảng viên', tracking=True)
    th_production_status = fields.Selection([
        ('not_started', 'Chưa sản xuất'),
        ('in_progress', 'Đang sản xuất'),
        ('done', 'Đã sản xuất')
    ], string='Tình trạng sản xuất', tracking=True)
    th_evaluation_status = fields.Selection([
        ('approved', 'Đã duyệt'),
        ('sent', 'Đã gửi'),
        ('not_approved', 'Chưa duyệt'),
        ('not_sent', 'Chưa gửi')
    ], string='Tình trạng thẩm định', tracking=True)
    th_printing_status = fields.Selection([
        ('not_printed', 'Chưa in'),
        ('printed', 'Đã in'),
        ('delivered_school', 'Bàn giao trường'),
        ('delivered_accounting', 'Bàn giao kế toán')
    ], string='Tình trạng in ấn', tracking=True)
    th_progress = fields.Float(string='Tiến độ (%)')
    th_target = fields.Char(string='Chỉ tiêu')

    # Các trường đồng bộ từ lpm1
    th_type = fields.Selection([('theory', 'Lý thuyết'),
                                ('practice', 'Thực hành'),
                                ('project', 'Đồ án')], string="Loại")
    th_origin_id = fields.Many2one('th.origin', string='Đơn vị sử dụng')
    th_language = fields.Selection([('vietnamese', 'Tiếng Việt'),
                                    ('english', 'Tiếng Anh')], string="Ngôn ngữ")
    th_object_type = fields.Selection([('base', 'Cơ sở'),
                                       ('specialized', 'Chuyên ngành'),
                                       ('outline', 'Đại cương'),
                                       ('internship', 'Thực tập'),
                                       ('project', 'Đề án,đồ án'),
                                       ('practice', 'Thực tập - rèn luyện')], string="Phân loại môn")
    th_user_id = fields.Many2one('res.users', string='Người phụ trách')
    th_major_ids = fields.Many2many('th.major', string='Ngành học')
    th_user_id_domain = fields.Char(compute='_compute_user_id_domain', string='Domain người phụ trách')

    th_type_course = fields.Selection([
        ('short', 'Ngắn hạn'),
        ('long', 'Dài hạn'),
    ], string='Loại khóa học')

    # Computed field để đếm số lượng cho pivot view
    th_count = fields.Integer(string='Số lượng', compute='_compute_count', store=False)
    th_production_office = fields.Char(string='Phòng sản xuất', related='th_project_id.th_production_office_id.name')
    
    @api.depends('th_user_id')
    def _compute_user_id_domain(self):
        for record in self:
            if record.th_project_id:
                # Lấy danh sách người phụ trách từ dự án
                user_ids = record.th_project_id.th_production_office_id.th_user_ids.ids
                record.th_user_id_domain = json.dumps([('id', 'in', user_ids)])
            else:
                record.th_user_id_domain = json.dumps([])

    def _compute_count(self):
        """Computed field để đếm số lượng records cho pivot view"""
        for record in self:
            record.th_count = 1

    # Kiểm tra mã và tên khóa học có trùng lặp không
    @api.constrains('th_course_code')
    def _th_check_unique_code_name(self):
        for rec in self:
            domain = [('id', '!=', rec.id), ('th_course_code', '=', rec.th_course_code)]
            if self.search_count(domain):
                raise ValidationError('Không được trùng mã!')

    # gán người phụ trách cho các giai đoạn dựa trên th_stage_ids
    @api.constrains('th_stage_ids')
    def _th_stage_responsible(self):
        for record in self:
            # Duyệt qua các stage để gán giá trị
            for stage in record.th_stage_ids:
                if stage.th_stage == 'content':
                    record.th_content_responsible_id = stage.th_responsible_id
                elif stage.th_stage == 'recording':
                    record.th_recording_responsible_id = stage.th_responsible_id
                elif stage.th_stage == 'editing':
                    record.th_editing_responsible_id = stage.th_responsible_id
                elif stage.th_stage == 'publishing':
                    record.th_publishing_responsible_id = stage.th_responsible_id

    # Kiểm tra mã và tên khóa học có trùng lặp không
    @api.constrains('th_course_code', 'th_aum_code')
    def _check_th_code_constraints(self):
        for record in self:
            # Check for special characters
            pattern = r'^[a-zA-Z0-9-_]+$'

            if record.th_course_code:
                if not re.match(pattern, record.th_course_code):
                    raise ValidationError(_('Mã chỉ được chứa chữ cái, số và dấu gạch ngang'))

                # Check uniqueness for course code
                domain = [('id', '!=', record.id), ('th_course_code', '=', record.th_course_code)]
                if self.search_count(domain):
                    raise ValidationError(_('Mã này đã tồn tại, vui lòng nhập mã khác'))

            if record.th_aum_code:
                if not re.match(pattern, record.th_aum_code):
                    raise ValidationError(_('Mã AUM chỉ được chứa chữ cái, số và dấu gạch ngang'))

                # Check uniqueness for AUM code
                domain = [('id', '!=', record.id), ('th_aum_code', '=', record.th_aum_code)]
                if self.search_count(domain):
                    raise ValidationError(_('Mã AUM đã tồn tại, vui lòng nhập mã khác'))

    # kiểm tra số tín chỉ là số nguyên dương
    @api.constrains('th_credit')
    def _th_check_positive_credit(self):
        for record in self:
            if record.th_credit <= 0:
                raise ValidationError(_('Số tín chỉ phải lớn hơn 0'))

    # kiểm tra các trường bắt buộc khi import dữ liệu
    @api.model
    def create(self, vals):
        is_import = '__import_row__' in vals or self.env.context.get('import_file')
        if is_import:
            required_fields = {
                'th_project_template_id': _("Tên học phần"),
                'th_course_code': _("Mã học phần"),
                'th_credit': _("Số tín chỉ"),
                'th_lecturer_id': _("Giảng viên phụ trách"),
                'th_production_standard': _("Tiêu chuẩn sản xuất"),
            }
            for field, label in required_fields.items():
                if not vals.get(field):
                    raise ValidationError(_("Thiếu trường bắt buộc: %s") % label)

        return super().create(vals)

    @api.model
    def get_import_templates(self):
        return [
            {
                'label': _('Mẫu Import Khóa học'),
                'template': '/th_lpm2/static/xls/import_khoahoc.xlsx'
            },
            {
                'label': _('Mẫu Import Học phần'),
                'template': '/th_lpm2/static/xls/import_hocphan.xlsx'
            }
        ]

    # kiểm tra ngày bắt đầu và kết thúc sản xuất
    @api.constrains('th_production_start_date', 'th_production_end_date', 'th_project_id')
    def _th_check_date_constraints(self):
        for record in self:
            # Kiểm tra ngày bắt đầu và kết thúc
            if record.th_production_start_date and record.th_production_end_date:
                if record.th_production_start_date > record.th_production_end_date:
                    raise ValidationError(_('Ngày bắt đầu không được lớn hơn ngày kết thúc'))

            # Kiểm tra ngày nằm trong khoảng thời gian của dự án
            if record.th_project_id:
                project = record.th_project_id
                if project.th_start_date and project.th_end_date:
                    if record.th_production_start_date:
                        if not project.th_start_date <= record.th_production_start_date <= project.th_end_date:
                            raise ValidationError(_('Ngày bắt đầu phải nằm trong khoảng từ %s đến %s của dự án')
                                                  % (project.th_start_date.strftime('%d/%m/%Y'),
                                                     project.th_end_date.strftime('%d/%m/%Y')))

                    if record.th_production_end_date:
                        if not project.th_start_date <= record.th_production_end_date <= project.th_end_date:
                            raise ValidationError(_('Ngày kết thúc phải nằm trong khoảng từ %s đến %s của dự án')
                                                  % (project.th_start_date.strftime('%d/%m/%Y'),
                                                     project.th_end_date.strftime('%d/%m/%Y')))

    # mở wizard cập nhật học phần
    def th_action_update_course_wizard(self):
        return {
            'type': 'ir.actions.act_window',
            'name': _('Cập nhật học phần'),
            'res_model': 'th.update.course.wizard',
            'view_mode': 'form',
            'target': 'new',
            'context': {
                'active_ids': self.ids,
                'active_model': 'th.course.lpm2',
            }
        }

    # Mở wizard import tiến độ sản xuất
    def th_action_open_import_production_stage(self):
        return {
            'name': _('Import Tiến độ sản xuất'),
            'type': 'ir.actions.act_window',
            'res_model': 'th.production.stage.wizard',
            'view_mode': 'form',
            'target': 'new',
            'context': {'default_course_id': self.id}
        }

    # Export tiến độ sản xuất ra file Excel
    def th_action_export_data_production_stage(self):
        wizard = self.env['th.production.stage.wizard'].create({
            'course_id': self.id
        })
        return wizard.th_export_production_stage()

    # mở wizard cập nhật khóa học (cho short course)
    def th_action_update_course_short_wizard(self):
        return {
            'type': 'ir.actions.act_window',
            'name': _('Cập nhật khóa học'),
            'res_model': 'th.update.course.short.wizard',
            'view_mode': 'form',
            'target': 'new',
            'context': {
                'active_ids': self.ids,
                'active_model': 'th.course.lpm2',
            }
        }

    # Mở wizard import tiến độ sản xuất (cho khóa học - short course)
    def th_action_open_import_production_stage_short(self):
        return {
            'name': _('Import Tiến độ sản xuất khóa học'),
            'type': 'ir.actions.act_window',
            'res_model': 'th.production.stage.short.wizard',
            'view_mode': 'form',
            'target': 'new',
            'context': {'default_course_id': self.id}
        }

    # Export tiến độ sản xuất ra file Excel (cho khóa học - short course)
    def th_action_export_data_production_stage_short(self):
        wizard = self.env['th.production.stage.short.wizard'].create({
            'course_id': self.id
        })
        return wizard.th_export_production_stage()

    @api.model
    def create(self, vals):
        if vals.get('th_project_id',False):
            # check origin ngắn hạn hoặc dài hạn
            th_project = self.env['th.project.lpm2'].browse(vals.get('th_project_id'))
            if th_project.th_origin_id.th_module_ids.name == 'APM':
                vals['th_type_course'] = 'short'
            elif th_project.th_origin_id.th_module_ids.name == 'CRM':
                vals['th_type_course'] = 'long'
            else:
                raise ValidationError(_('Dự án phải thuộc trong ngắn hạn hoặc dài hạn'))
        
        # lấy action hiện tại 
        if request and request.session:
            current_action_id = request.session.get('current_action_id')
            if current_action_id:
                action = self.env['ir.actions.act_window'].browse(current_action_id)
        
        return super().create(vals)
