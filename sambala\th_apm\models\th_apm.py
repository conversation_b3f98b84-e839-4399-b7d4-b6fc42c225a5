import json
import random
from markupsafe import Markup

from odoo import api, fields, models, _, exceptions
from odoo.exceptions import ValidationError
from odoo.osv import expression
from datetime import datetime, timedelta
import xmlrpc.client
from odoo.exceptions import UserError

ORIGIN_LINE = ['th_setup_parameters.th_origin_vmc', 'th_setup_parameters.th_origin_vstep', ]


class APMLead(models.Model):
    _name = "th.apm"
    _inherit = ['mail.thread', 'mail.activity.mixin']
    _description = "Cơ hội"

    # def _domain_th_origin(self):
    #     rec = self.env.ref('th_setup_parameters.th_apm_module')
    #     return [('th_module_ids', 'in', rec.ids)]

    active = fields.Boolean(default=True)
    name = fields.Char(string='Tên c<PERSON> hội', readonly=True, required=True, default="MỚI", copy=False)
    name_id_sequence = fields.Char(copy=False)
    th_customer_code = fields.Char(string="<PERSON><PERSON> khách hàng", related="th_partner_id.th_customer_code" )
    th_partner_id = fields.Many2one(comodel_name="res.partner", string="<PERSON>h<PERSON><PERSON> hàng", index=True, required=True,
                                    tracking=True)
    th_partner_reference_id = fields.Many2one(comodel_name="res.partner", string="Người giới thiệu", copy=False, index=True)
    th_readonly_partner_reference = fields.Boolean(string='Readonly trường người giới thiệu', default=False, compute='_compute_readonly_partner_reference')
    th_apm_phone = fields.Char(string="Số điện thoại")
    th_apm_email = fields.Char(string="Email")
    th_partner_phone = fields.Char(string="Số điện thoại", related="th_partner_id.phone", )
    th_apm_contact_trait_ids = fields.One2many(related="th_partner_id.th_apm_contact_trait_ids",readonly=False)
    th_partner_email = fields.Char(string="Email partner", related="th_partner_id.email")
    th_need_id = fields.Many2one(comodel_name="th.apm.need", string="Nhu cầu")
    th_product_ids = fields.Many2many(comodel_name="product.product", string="Sản phẩm")
    th_course_type = fields.Selection([
        ('basic', 'Khóa cơ bản'),
        ('intensive', 'Khóa cấp tốc')
    ], string='Khóa học', tracking=True)
    th_user_id = fields.Many2one('res.users', string='Người chăm sóc', tracking=True)
    th_reason = fields.Text(string="Lý do ngừng chăm sóc", tracking=True)
    # Các trường xử lý cơ hội trùng
    th_is_a_duplicate_opportunity = fields.Boolean(string="Là cơ hội trùng lặp", default=False, copy=False)
    th_duplicate_processed_lead = fields.Boolean(string="Cơ hội đã xử lý trùng", copy=False, default=False)
    th_duplicate_description = fields.Text(string="Mô tả kiểm tra trùng", copy=False)
    th_duplicate_date = fields.Date(string="Ngày kiểm tra trùng", copy=False)
    th_duplicate_type = fields.Selection(selection=[('manual', 'Thủ công'), ('auto', 'Tự động'),
                                                   ('need_handle', 'Cần xử lý'), ('no_results', 'Chưa có điều kiện')],
                                         string="Loại xử lý", copy=False, default='auto')
    th_dup_state = fields.Selection(string='Trạng thái', selection=[('processing', 'Đang xử lý'), ('processed', 'Đã xử lý')],
                                   default=False)
    th_selection_dup_result = fields.Selection(selection=[('keep', 'Trùng-Giữ'), ('change', 'Trùng-Chuyển')],
                                              string="Kết quả", default=False)
    th_lead_apm_source_id = fields.Many2one("th.apm", 'ID cơ hội APM gốc', copy=False)
    th_dup_need_admin = fields.Boolean(string="Cần admin xử lý", default=False, copy=False)
    th_is_under_complaint = fields.Boolean(string="Đang bị khiếu nại", default=False, copy=False)
    th_is_close_lead = fields.Boolean(string="Cơ hội bị đóng", default=False, copy=False)   #chưa sử dụng
    # th_source_id = fields.Many2one(comodel_name="th.source.group", string="Nguồn", tracking=True)
    th_channel_id = fields.Many2one(comodel_name="th.info.channel", string="Kênh", tracking=True)
    th_ownership_unit_id = fields.Many2one(comodel_name="th.ownership.unit", string="Đơn vị sở hữu", tracking=True)
    th_stage_id = fields.Many2one(comodel_name="th.apm.level", string="Mối quan hệ", tracking=True,
                                  default=lambda self: self.env['th.apm.level'].search(
                                      [('th_first_status', '=', True)]).id,
                                  readonly=False, store=True)
    th_status_detail_id = fields.Many2one(comodel_name="th.status.detail", string="Trạng thái chi tiết",
                                          domain="[('th_apm_level_ids', '=?', th_stage_id), ('th_status_category_id', '=?', th_status_category_id)]",
                                          tracking=True, required=True)
    th_description = fields.Text(string='Mô tả', tracking=True)
    th_apm_team_id = fields.Many2one(comodel_name="th.apm.team", string="Đội chăm sóc")
    th_can_create_order = fields.Boolean(related='th_stage_id.th_can_create_order')
    th_last_status_stage = fields.Boolean(compute='_compute_status_stage_domain')   #không sử dụng logic chuyển đơn hàng cho trạng thái cuối cùng
    th_sale_order_count = fields.Boolean(compute='_compute_sale_order_count')
    th_order_created = fields.Boolean()
    th_apm_lead_trait_ids = fields.One2many("th.apm.lead.trait", "th_apm_lead_id", string="Đặc điểm", copy=True,
                                           readonly=0)
    sale_order_count = fields.Integer(compute='_compute_sale_order_count1')
    th_origin_id = fields.Many2one(comodel_name='th.origin', string="Dòng sản phẩm")
    th_product_category_id = fields.Many2one(string="Nhóm sản phẩm", comodel_name='product.category')
    th_status_category_id = fields.Many2one(comodel_name='th.status.category', string="Nhóm trạng thái",
                                            tracking=True,
                                            required=True)
    th_processing_status_id = fields.Many2one(comodel_name='th.processing.status', string="Trạng thái xử lý",
                                              domain="[('th_status_detail_id', '=?', th_status_detail_id)]",
                                              tracking=True)
    th_opportunity_aff_id = fields.Integer(string="Cơ hội AFF", copy=False)
    th_order_id = fields.One2many('sale.order', 'th_apm_id', string='Sale Order', readonly=True, copy=False)
    th_state_detail_domain = fields.Char(compute='_compute_th_state_detail_domain')
    th_product_category_domain = fields.Char(compute='_compute_th_product_category_domain')
    th_product_domain = fields.Char(compute='_compute_th_product_domain')
    th_user_id_domain = fields.Char(compute='_compute_th_user_id_domain')
    th_product_line_domain = fields.Char(compute='_compute_th_product_category_domain')
    th_apm_team_domain = fields.Char(compute='_domain_th_apm_team')
    th_order_vmc_name = fields.Char('Tên đơn hàng vmc', copy=False)
    th_data_getfly = fields.Text(string="Data Getfly", copy=False)
    th_last_check = fields.Datetime(string="Liên hệ lần cuối", default=lambda self: fields.Datetime.now(),
                                   tracking=True)
    th_affiliate_code = fields.Char(string="Mã Tiếp Thị Liên Kết", related="th_partner_reference_id.th_affiliate_code",
                                    store=True, copy=False)
    th_check_partner_referred = fields.Boolean(copy=False)
    th_source_group_id = fields.Many2one(comodel_name="th.source.group", string="Nhóm nguồn", tracking=True,
                                         copy=False,)
    th_after_sales_care = fields.Boolean(string="Cơ hội sau chăm sóc", default=False)
    th_stage_id_domain = fields.Char(compute='_compute_th_stage_id_domain')
    th_level_up_date = fields.Date('Ngày lên level', compute='_compute_th_level_up_date', store=True, default=False)
    th_campaign_id = fields.Many2one(comodel_name="th.apm.campaign", string="Chiến dịch",
                                     domain="th_campaign_domain",
                                     )
    th_check_group_admin_domain = fields.Boolean(compute='_compute_check_group_admin_domain', default=True)
    th_opportunity_list_partner_ids = fields.One2many("th.apm.opportunity.list.partner", "th_apm_id",
                                                      copy=True, compute="_compute_th_opportunity_list_partner_ids")
    th_status_payment = fields.Boolean(string="Tình trạng thanh toán", compute="_compute_th_status_payment")
    th_is_lead_TTVH = fields.Boolean(string="Đơn hàng của vận Hành", default=False)
    th_origin_ids = fields.Many2many(comodel_name='th.origin', string="Nhóm dòng sản phẩm")
    th_order_history_ids = fields.One2many(comodel_name='th.order.history', inverse_name="th_apm_id",
                                           string="Lịch sử mua hàng", compute="compute_order_history")
    th_check_phone = fields.Char(string="Điện thoại")
    th_check_email = fields.Char(string="Email import")
    name_import = fields.Char(string="Tên import")

    th_campaign_domain = fields.Char(compute='_compute_th_campaign_domain')
    th_student_status_ids = fields.One2many(comodel_name='th.apm.student.status', inverse_name="th_lead_apm_id",
                                           string="Tình trang học viên")
    th_check_c = fields.Char(compute="compute_th_student_status",compute_sudo=True)
    th_status_category_domain = fields.Char(compute="compute_status_category")
    th_resign_id = fields.Many2one(comodel_name='th.apm.resign', string="Tái Ký")
    th_detail_resign_id = fields.Many2one(comodel_name='th.detail.apm.resign', string="Chi tiết tái Ký", tracking=True, domain="[('th_resign_id', '=?', th_resign_id)]")
    th_utm_source = fields.Char('utm source', copy=False)
    th_utm_medium = fields.Char('utm medium', copy=False)
    th_utm_campaign = fields.Char('utm campaign', copy=False)
    th_utm_term = fields.Char('utm term', copy=False)
    th_utm_content = fields.Char('utm content', copy=False)
    th_import_phone = fields.Char(string="Check import Điện thoại")
    th_import_email = fields.Char(string="Check import Email")
    th_old_partner_id = fields.Many2one('res.partner')
    th_day_recent_purchase = fields.Datetime('Ngày mua gần nhất', compute="_compute_recent_purchase", readonly=1,
                                             tracking=True)
    th_day_recent_purchase_filter = fields.Datetime('Ngày mua gần nhất', related="th_day_recent_purchase",store=True)
    th_first_day_purchase = fields.Date('Ngày mua lần 1', compute="_compute_first_purchase", readonly=1,
                                        tracking=True)
    th_ecommerce_platform = fields.Boolean(string="Đơn hàng từ sàn", default=False)
    th_product_category_ids = fields.Many2many('product.category','products_category_rel', string="Nhóm sản phẩm sau bán")
    th_product_categorys_domain = fields.Char(compute="_compute_product_category_after_sale")
    th_customer_code_aum = fields.Char(string='Mã khách hàng')
    th_apm_dividing_ring_id = fields.Many2one('th.apm.dividing.ring', string="Vòng chia", domain="th_apm_dividing_ring_domain")
    th_apm_dividing_ring_domain = fields.Char(compute="_compute_th_apm_dividing_ring_domain")
    # thêm một sô trường cho cơ hội sau bán
    th_student_code = fields.Char("Mã sinh viên")
    th_shift_selection = fields.Selection([('morning', 'Ca sáng'), ('afternoon', 'Ca chiều')],
                                          tracking=True, string="Ca Test", default="")
    th_test_date = fields.Date("Ngày test")
    th_test_result = fields.Selection([('A0', 'Trình độ A0'),
                                       ('A1', 'Trình độ A1'),
                                       ('A2', 'Trình độ A2'),
                                       ('B1', 'Trình độ B1')],
                                      tracking=True,
                                      string="Kết quả Test", default= ""
                                      )
    th_check_vstep_field = fields.Boolean("Check ẩn hiện", compute="_compute_show_extra_fields", store= True)
    th_source_name = fields.Char(string="Tên nguồn", tracking=True)
    th_auto_update_products = fields.Boolean(string="Tự động cập nhật sản phẩm", default=False, compute = '_compute_auto_update_products')
    th_readonly_domain = fields.Char(compute="_th_compute_readonly_domain")
    th_estimated_level_up_date = fields.Date(string="Ngày dự kiến lên L8")
    th_marker_origin = fields.Selection(string="Trường đánh dấu các dòng sản phẩm", selection=[('none', 'Không'), ('vmc', 'VMC'), ('vstep', 'VSTEP')], compute="_compute_th_marker_origin")
    def _th_compute_readonly_domain(self):
        for rec in self:
            if rec.th_ownership_unit_id.th_type == "other" and rec.env.user.has_group(
                    'th_apm.apm_group_partner_manager') and not rec.env.user.has_group(
                'th_apm.group_apm_administrator'):
                rec.th_readonly_domain = json.dumps([])
            else:
                rec.th_readonly_domain = False

    @api.depends('th_after_sales_care', 'th_order_history_ids')
    def _compute_auto_update_products(self):
        """Compute field kích hoạt tự động cập nhật sản phẩm"""
        for rec in self:
            rec.th_auto_update_products = rec.th_after_sales_care and bool(rec.th_order_history_ids)

            if rec.th_auto_update_products:
                rec._update_products_from_history()
    # Add inside the APMLead class

    def _update_products_from_history(self):
        """Cập nhật sản phẩm và dòng sản phẩm từ lịch sử mua hàng"""
        self.ensure_one()

        if not self.th_after_sales_care or not self.th_order_history_ids:
            return

        product_ids = set()
        origin_ids = set()

        # Lấy danh sách sản phẩm và dòng sản phẩm từ lịch sử mua hàng
        for order_history in self.th_order_history_ids:
            for product in order_history.th_product_ids:
                product_ids.add(product.id)

            if order_history.th_origin_id:
                origin_ids.add(order_history.th_origin_id.id)

        if product_ids and not product_ids.issubset(set(self.th_product_ids.ids)):
            for product_id in product_ids:
                self.th_product_ids = [(4, product_id, 0)]

        if origin_ids and not origin_ids.issubset(set(self.th_origin_ids.ids)):
            for origin_id in origin_ids:
                self.th_origin_ids = [(4, origin_id, 0)]

            # Nếu chỉ có 1 dòng sản phẩm, tự động gán vào trường th_origin_id
            if len(origin_ids) == 1:
                self.th_origin_id = list(origin_ids)[0]

    @api.depends('th_origin_id', 'th_origin_ids')
    def _compute_th_marker_origin(self):
        for rec in self:
            if rec.th_origin_id and rec.th_origin_id.name in self.env.ref('th_setup_parameters.th_origin_vstep').name:
                rec.th_marker_origin = 'vstep'
            elif rec.th_origin_id and rec.th_origin_id.name in self.env.ref('th_setup_parameters.th_origin_vmc').name:
                rec.th_marker_origin = 'vmc'
            else:
                rec.th_marker_origin = 'none'

    @api.depends("th_origin_ids")
    def _compute_show_extra_fields(self):
        for rec in self:
            if rec.th_after_sales_care == True:
                if self.env.ref('th_setup_parameters.th_origin_vstep').name in rec.th_origin_ids.mapped('name'):
                    rec.th_check_vstep_field = True
                else:
                    rec.th_check_vstep_field = False

    def _compute_readonly_partner_reference(self):
        for rec in self:
            if rec.env.user in rec.th_user_id or rec.env.user in rec.th_user_id.th_manager_apm_ids or self.env.user.has_group(
                    'th_apm.group_apm_administrator'):
                if rec.th_order_id:
                    if all(state == 'draft' for state in rec.th_order_id.mapped('state')):
                        rec.th_readonly_partner_reference = False
                    else:
                        rec.th_readonly_partner_reference = True
                else:
                    rec.th_readonly_partner_reference = False
            else:
                rec.th_readonly_partner_reference = True


    @api.depends('th_product_category_ids')
    def _compute_product_category_after_sale(self):
        for rec in self:
            domain = []
            category_ids = rec.env['product.category'].search([('th_module_ids', 'in', rec.env.ref('th_setup_parameters.th_apm_module').ids)]).ids
            domain.append(('id', 'in', category_ids))
            rec.th_product_categorys_domain = json.dumps(domain)

    @api.onchange('th_ecommerce_platform')
    def onchange_th_ecommerce_platform(self):
        self.ensure_one()
        if self.th_ecommerce_platform:
            self.th_check_phone = self._generate_unique_phone_number()

    def _generate_unique_phone_number(self):
        phone_number = '0'
        last_digit = None
        for _ in range(9):
            available_digits = [str(d) for d in range(10) if str(d) != last_digit]
            new_digit = random.choice(available_digits)
            phone_number += new_digit
            last_digit = new_digit
        return phone_number
    @api.onchange('th_check_phone')
    def onchange_th_check_phone(self):
        self.ensure_one()
        check_module = self.env['ir.config_parameter'].sudo().get_param('th_check_partner_module')
        if self.th_check_phone:
            domain = ['|', ('phone', '=', self.th_check_phone), ('th_phone2', '=', self.th_check_phone)]
            if check_module:
                domain.append(('th_module_ids', 'in', self.env.ref('th_setup_parameters.th_apm_module').ids))
            partner = self.env['res.partner'].search(domain, limit=1)

            self.th_partner_id = partner

    @api.onchange('th_check_email')
    def onchange_th_check_email(self):
        self.ensure_one()
        check_module = self.env['ir.config_parameter'].sudo().get_param('th_check_partner_module')
        if self.th_check_email:
            domain = [('email', '=', self.th_check_email)]
            if check_module:
                domain.append(('th_module_ids', 'in', self.env.ref('th_setup_parameters.th_apm_module').ids))
            partner = self.env['res.partner'].search(domain, limit=1)
            self.th_partner_id = partner
    @api.depends('th_campaign_id')
    def _compute_th_campaign_domain(self):
        for record in self:
            domain = []
            if record.env.user.has_group('th_apm.group_leader_apm_after_order') and not record.env.user.has_group('th_apm.group_apm_leader'):
                campaign = self.env['th.apm.campaign'].search(['|', ('id', '=', record.env.ref('th_apm.campaign_after_sales_care').id), ('th_check_group','=', True), ('state', '=', 'approve')])
                domain.append(('id', 'in', campaign.ids))
            elif record.env.user.has_group('th_apm.group_apm_leader') or record.env.user.has_group('th_apm.group_apm_user'):
                campaign = self.env['th.apm.campaign'].search([('id', '!=', record.env.ref('th_apm.campaign_lead_formio').id), ('id', '!=', record.env.ref('th_apm.campaign_lead_auto').id), ('state', '=', 'approve'), ('th_origin_id', '!=', False)])
                if self._context.get('is_after_order'):
                    campaign = campaign + self.env['th.apm.campaign'].search([('id', '=', record.env.ref('th_apm.campaign_after_sales_care').id)])
                domain.append(('id', 'in', campaign.ids))
            record.th_campaign_domain = json.dumps(domain)


    @api.depends('th_order_id')
    def _compute_th_status_payment(self):
        for rec in self:
            if rec.th_order_id:
                if rec.th_order_id.invoice_ids.mapped('th_payment_status') != [] and len(rec.th_order_id) == len( rec.th_order_id.invoice_ids) \
                        and 'not_paid' not in rec.th_order_id.invoice_ids.mapped('th_payment_status'):
                    rec.th_status_payment = True
                else:
                    rec.th_status_payment = False
            else:
                rec.th_status_payment = False

    @api.depends('th_partner_id')
    def _compute_th_opportunity_list_partner_ids(self):
        for record in self:
            list_ids = []
            record.th_opportunity_list_partner_ids = False
            if record.th_partner_id:
                opportunities = self.env['th.apm'].sudo().search(
                    [('th_partner_id', '=', record.th_partner_id.id), ('th_reason', '=', False),
                     ('th_is_a_duplicate_opportunity', '=', False), ('id', 'not in', record.ids),('th_origin_id','=', record.th_origin_id.id)])
                for rec in opportunities:
                    opportunity_list_id = self.env['th.apm.opportunity.list.partner'].create({
                        'name': rec.name,
                        'th_last_check': rec.th_last_check,
                        'th_origin_id': rec.th_origin_id.id,
                        'th_campaign_id': rec.th_campaign_id.id,
                        'th_stage_id': rec.th_stage_id.id,
                        'th_ownership_unit_id': rec.th_ownership_unit_id.id
                    })
                    list_ids.append(opportunity_list_id.id)
                record.th_opportunity_list_partner_ids = [(6, 0, list_ids)]

    # @api.onchange('th_user_id')
    # def onchange_th_user_id(self):
    #     if self.th_user_id:
    #         self.th_apm_team_id =

    @api.model
    def _compute_check_group_admin_domain(self):
        for record in self:
            if record.env.user.has_group('th_apm.group_apm_leader') or record.env.user.has_group('th_apm.group_leader_apm_after_order'):
                record.th_check_group_admin_domain = True
            else:
                record.th_check_group_admin_domain = False

    @api.model
    def default_get(self, field_list):
        result = super().default_get(field_list)
        result['th_status_category_id'] = self.env.ref('th_apm.th_no_process_category').id
        result['th_status_detail_id'] = self.env.ref('th_apm.th_no_process_detail').id
        return result

    @api.depends('th_after_sales_care', 'th_origin_id')
    def _compute_th_stage_id_domain(self):
        for record in self:
            domain = []
            if record.th_origin_id:
                domain.append(('th_origin_ids', 'in', [record.th_origin_id.id]))
            if not record.th_after_sales_care:
                domain.append(('th_status_after_sales_care', '=', False))
            elif record.th_after_sales_care:
                domain.append(('is_after_sale', '=', True))
            record.th_stage_id_domain = json.dumps(domain)

    @api.depends('th_after_sales_care')
    def compute_status_category(self):
        for record in self:
            if not record.th_after_sales_care:
                domain = [('th_categ_after_sale', '=', False), ('th_type', '=', 'apm'),
                          ('th_apm_level_category', '=?', record.th_stage_id.id)]
            elif record.th_after_sales_care:
                domain = [('th_categ_after_sale', '=', True), ('th_type', '=', 'apm'),
                          ('th_apm_level_category', '=?', record.th_stage_id.id)]
            record.th_status_category_domain = json.dumps(domain)
    @api.depends('th_stage_id')
    def _compute_status_stage_domain(self):
        for record in self:
            if not record.th_after_sales_care and record.th_stage_id.th_last_status:
                record.th_last_status_stage = True
            elif record.th_after_sales_care and record.th_stage_id.is_after_sale:
                record.th_last_status_stage = True
            else:
                record.th_last_status_stage = False


    def _default_stage_id(self):
        level = self.env['th.apm.level'].search([('th_first_status', '=', True)], limit=1)
        return level.id or False

    def update_th_last_check(self):
        self.update({'th_last_check': fields.Datetime.now()})


    @api.depends('th_campaign_id', 'th_user_id')
    def _domain_th_apm_team(self):
        domain = []
        for rec in self:
            if rec.th_campaign_id:
                domain.append(('id', 'in', rec.th_campaign_id.th_apm_team_ids.ids))
            if rec.th_user_id:
                team_ids = self.env['th.apm.team'].search(['|', ('manager_id', '=', rec.th_user_id.id),
                                                           ('th_member_ids', 'in', rec.th_user_id.id)])
                domain.append(('id', 'in', team_ids.ids))
            else:
                rec.th_apm_team_domain = []
            rec.th_apm_team_domain = json.dumps(domain)

    @api.depends('th_campaign_id')
    def _compute_th_apm_dividing_ring_domain(self):
        for record in self:
            if record.th_campaign_id:
                domain = [('id', 'in', record.th_campaign_id.th_apm_divide_ring_ids.ids),('th_is_opportunity_dividing', '=', True)]
            else:
                domain = []
            record.th_apm_dividing_ring_domain = json.dumps(domain)

    @api.onchange('th_campaign_id')
    def onchange_th_campaign_id(self):
        if self.th_campaign_id:
            # Nếu thay đổi chiến dịch, vòng chia th_apm_dividing_ring nằm trong chiến dịch thì không thay đổi
            if self.th_apm_dividing_ring_id.id and self.th_apm_dividing_ring_id.id not in self.th_campaign_id.th_apm_divide_ring_ids.ids:
                self.th_apm_dividing_ring_id = False

    @api.onchange('th_apm_dividing_ring_id')
    def onchange_th_apm_dividing_ring_id(self):
        """Khi thay đổi vòng chia, tự động chia cơ hội và gán người thực hiện"""
        # Đánh dấu trạng thái vòng chia
        if self.th_apm_dividing_ring_id:
            try:
                # Gọi hàm phân chia cơ hội từ vòng chia
                user_id = self.th_apm_dividing_ring_id.action_assign_leads_dividing_ring()
                if user_id:
                    self.th_user_id = user_id
            except Exception:
                # Xử lý lỗi nếu có - không gán người thực hiện
                self.th_user_id = False

    @api.onchange('th_campaign_id')
    def _onchange_th_origin_id(self):
        for record in self:
            if record.th_campaign_id:
                record.th_product_category_id = record.th_campaign_id.th_product_category_id.id
                record.th_origin_id = record.th_campaign_id.th_origin_id.id
                record.th_product_ids = [(6, 0, record.th_campaign_id.th_product_ids.ids)]
                record.th_apm_team_id = record.th_campaign_id.th_apm_team_ids[0].id if len(
                    record.th_campaign_id.th_apm_team_ids) >= 1 else False
                record.th_origin_ids = record.th_campaign_id.th_origin_id.ids if record.th_is_lead_TTVH != False else False
                record.th_campaign_id = record.th_campaign_id
            else:
                record.th_product_category_id = False
                record.th_origin_id = False
                record.th_product_ids = False
                record.th_apm_team_id = False

    @api.onchange('th_campaign_id', 'th_product_category_id')
    def onchange_th_product_category(self):
        if self.th_product_category_id and self.th_product_ids and self.th_product_ids.mapped(
                'categ_id') != self.th_product_category_id:
            self.th_product_ids = False

    @api.onchange('th_status_category_id')
    def onchange_th_call_status(self):
        if self.th_status_detail_id and self.th_status_detail_id.id not in self.th_status_category_id.th_status_detail_ids.ids:
            self.th_status_detail_id = False

    def action_stop_caring(self):
        self.ensure_one()
        return {
            'type': 'ir.actions.act_window',
            'name': 'Dừng chăm sóc',
            'view_mode': 'form',
            'res_model': 'th.apm',
            'target': 'new',
            'res_id': self.id,
            'views': [[self.env.ref('th_apm.apm_stop_caring_form').id, 'form']],
        }

    def action_stop_caring_save(self):
        if not self.th_reason:
            raise exceptions.ValidationError('Bạn chưa chọn Lý do ngừng chăm sóc')

    def th_check_condition_lead(self, old_lead, new_lead):
        """
        Xử lý kiểm tra cơ hội trùng dựa trên ma trận điều kiện
        :param old_lead: Cơ hội cũ
        :param new_lead: Cơ hội mới
        :return: True nếu xử lý thành công, False nếu có lỗi
        """
        des, vals, vals_new, vals_old, vals_history = '', {}, {}, {}, {}
        try:
            today = fields.Date.today()
            old_lead = old_lead.sudo()
            if not old_lead or not new_lead:
                raise ValidationError('Không có giá trị: ' + str({'old_lead': old_lead, 'new_lead': new_lead}))
            if not old_lead.th_last_check or not new_lead.th_last_check:
                raise ValidationError('Không có giá trị ngày: ' + str({'old_th_last_check': old_lead.th_last_check, 'new_th_last_check': new_lead.th_last_check}))

            # Tính số ngày từ lần liên hệ cuối cùng đến hiện tại
            th_last_check = (fields.Date.today() - old_lead.th_last_check.date()).days

            # Tìm điều kiện kiểm tra trùng phù hợp
            check_condition = self.env['th.apm.check.condition'].search(
                [('th_date_from', '<=', th_last_check), ('th_date_to', '>', th_last_check),
                 ('th_apm_level_id', '=', old_lead.th_stage_id.id),
                 ('th_status_detail_id', '=', old_lead.th_status_detail_id.id),
                 '|', ('th_origin_ids', '=', False), ('th_origin_ids', 'in', [old_lead.th_origin_id.id])], limit=1)

            # Nếu không tìm thấy điều kiện, thử tìm không có dòng sản phẩm
            if not check_condition:
                check_condition = self.env['th.apm.check.condition'].search(
                    [('th_date_from', '<=', th_last_check), ('th_date_to', '>', th_last_check),
                     ('th_apm_level_id', '=', old_lead.th_stage_id.id),
                     ('th_status_detail_id', '=', old_lead.th_status_detail_id.id),
                     ('th_origin_ids', '=', False)], limit=1)

            # Lấy kết quả xử lý từ điều kiện
            check_result = check_condition.th_result if check_condition else False

            # Chuẩn bị dữ liệu cho lịch sử kiểm tra trùng
            data = []
            old_lead = old_lead.with_context(apm_create=True)
            new_lead = new_lead.with_context(apm_create=True)
            self = self.with_context(check_condition=check_result)
            # old_lead.th_last_check = fields.Datetime.now()

            # Xử lý dựa trên kết quả kiểm tra
            if check_result and not old_lead.sudo().th_reason:
                if check_result == 'keep':
                    # Giữ cơ hội cũ, đánh dấu cơ hội mới là trùng
                    new_lead.sudo().with_context(apm_create=True).write({
                        'th_is_a_duplicate_opportunity': True,
                        'th_duplicate_processed_lead': True,
                        'th_dup_state': 'processed',
                        'th_selection_dup_result': 'change',
                        'th_lead_apm_source_id': old_lead.id,
                        'th_duplicate_type': 'auto',
                        'th_duplicate_description': des,
                        'th_duplicate_date': today
                    })

                    # Cập nhật thông tin cho cơ hội cũ
                    old_lead.sudo().with_context(apm_create=True).write({
                        'th_duplicate_processed_lead': True,
                        'th_dup_state': 'processed',
                        'th_selection_dup_result': 'keep',
                        'th_duplicate_type': 'auto'
                    })

                    # Thêm mô tả
                    des = f"Cơ hội {new_lead.name}:" + f"Ngày trùng {new_lead.create_date.strftime('%d/%m/%Y')} \n" + (f"Mô tả {new_lead.th_description}" if new_lead.th_description else '')

                    # Thêm thông báo vào chatter
                    msg = _("Cơ hội %s đã được đánh dấu là trùng với cơ hội %s. Cơ hội %s được giữ lại.") % (
                        new_lead.name, old_lead.name, old_lead.name)
                    # old_lead.message_post(body=msg)
                    new_lead.message_post(body=msg)

                elif check_result == 'transfer':
                    
                    # Giữ cơ hội mới, đánh dấu cơ hội cũ là trùng
                    old_lead.sudo().with_context(apm_create=True).write({
                        'th_is_a_duplicate_opportunity': True,
                        'th_duplicate_processed_lead': True,
                        'th_dup_state': 'processed',
                        'th_selection_dup_result': 'change',
                        'th_lead_apm_source_id': new_lead.id,
                        'th_duplicate_type': 'auto'
                    })

                    # Cập nhật thông tin cho cơ hội mới
                    new_lead.sudo().with_context(apm_create=True).write({
                        'th_duplicate_processed_lead': True,
                        'th_dup_state': 'processed',
                        'th_selection_dup_result': 'keep',
                        'th_duplicate_type': 'auto'
                    })

                    # Thêm mô tả
                    des = f"\n Cơ hội {old_lead.name}:\n" + f"Ngày trùng {old_lead.create_date.strftime('%d/%m/%Y')} \n" + (f"Mô tả {old_lead.th_description}" if old_lead.th_description else '')

                    new_lead.message_ids = False
                    # Sao chép lịch sử chăm sóc từ cơ hội thua sang cơ hội thắng (từ cơ hội mới sang cơ hội cũ)
                    lose_note_ids = old_lead.sudo().message_ids.sorted('date')
                    
                    for note in lose_note_ids:
                        if note.message_type == 'notification' or note.message_type == 'comment':
                            note_new = note.copy()
                            note_new.date = note.sudo().date
                            note_new.sudo().res_id = new_lead.id
                            track_list = []
                            if note.tracking_value_ids:
                                for tracking in note.tracking_value_ids:
                                    new_track_value = tracking.copy()
                                    track_list.append(new_track_value.id)
                            note_new.sudo().tracking_value_ids = [(6, 0, track_list)]
                            
                    # Thêm thông báo vào chatter
                    msg = _("Cơ hội %s đã được đánh dấu là trùng với cơ hội %s. Cơ hội %s được giữ lại.") % (
                        old_lead.name, new_lead.name, new_lead.name)
                    old_lead.message_post(body=msg)
                    
                    apm_lead = self.env['th.apm'].search([('id', '=', old_lead.id)],limit=1)
                    if apm_lead:
                        apm_lead.with_user(self.env.ref('base.user_root')).sudo().message_post(body=msg1)
                    new_lead.with_context(th_test_import=True, crm_create=True).with_user(
                        self.env.ref('base.user_root')).sudo().message_post(body=msg2)
                    
                    msg_new_lead = _("Cơ hội mới vào theo check trùng ma trận - cơ hội được xử thắng!")
                    # new_lead.sudo().th_first_create_day = old_lead.sudo().create_date.date()
                    
                    new_lead.message_post(body=msg_new_lead)
                    
                    
                            
                # Tạo lịch sử kiểm tra trùng
                vals_history = {
                    'th_name_lead_lose': new_lead.name if check_result == 'keep' else old_lead.name,
                    'th_ownership_id': new_lead.th_ownership_unit_id.id if check_result == 'keep' else old_lead.th_ownership_unit_id.id,
                    'th_status_group_id': new_lead.th_status_category_id.id if check_result == 'keep' else old_lead.th_status_category_id.id,
                    'th_status_detail_id': new_lead.th_status_detail_id.id if check_result == 'keep' else old_lead.th_status_detail_id.id,
                    'th_stage_id': new_lead.th_stage_id.id if check_result == 'keep' else old_lead.th_stage_id.id,
                    'th_description': des,
                    'th_last_check': new_lead.th_last_check if check_result == 'keep' else old_lead.th_last_check,
                    'th_lead_apm_id_old': new_lead.id if check_result == 'keep' else old_lead.id,
                    'th_lead_apm_id_new': old_lead.id if check_result == 'keep' else new_lead.id,
                    'th_channel_id': new_lead.th_channel_id.id if check_result == 'keep' else old_lead.th_channel_id.id,
                    'user_id': new_lead.th_user_id.id if check_result == 'keep' else old_lead.th_user_id.id,
                    'th_source_name': new_lead.th_source_name if check_result == 'keep' else old_lead.th_source_name,
                    'th_source_group_id': new_lead.th_source_group_id.id if check_result == 'keep' else old_lead.th_source_group_id.id,
                    'th_partner_referred_id': new_lead.th_partner_reference_id.id if check_result == 'keep' else old_lead.th_partner_reference_id.id,
                    'th_duplicate_type': 'auto',
                    'th_origin_id': new_lead.th_origin_id.id if check_result == 'keep' else old_lead.th_origin_id.id,
                }

            elif not check_result and not old_lead.sudo().th_reason:
                # Nếu không có điều kiện hoặc cơ hội cũ đã ngừng chăm sóc
                if old_lead.th_reason:
                    new_lead.with_context(th_test_import=True, apm_create=True).sudo().write({
                        'th_dup_need_admin': True,
                        'th_is_a_duplicate_opportunity': True,
                        'th_duplicate_type': 'no_results'
                    })
                new_lead.with_context(th_test_import=True, apm_create=True).sudo().write({
                    'th_is_a_duplicate_opportunity': True,
                    'th_duplicate_type': 'no_results'
                })

                # tạo lịch sử kiểm tra trùng với th_duplicate_type = no_results
                vals_history = {
                    'th_name_lead_lose': new_lead.name,
                    'th_ownership_id': new_lead.th_ownership_unit_id.id,
                    'th_status_group_id': new_lead.th_status_category_id.id,
                    'th_status_detail_id': new_lead.th_status_detail_id.id,
                    'th_stage_id': new_lead.th_stage_id.id,
                    'th_description': new_lead.th_description,
                    'th_last_check': new_lead.th_last_check,
                    'th_lead_apm_id_old': new_lead.id,
                    'th_lead_apm_id_new': old_lead.id,
                    'th_channel_id': new_lead.th_channel_id.id,
                    'user_id': new_lead.th_user_id.id,
                    'th_source_name': new_lead.th_source_name,
                    'th_source_group_id': new_lead.th_source_group_id.id,
                    'th_partner_referred_id': new_lead.th_partner_reference_id.id,
                    'th_duplicate_type': 'no_results',
                    'th_origin_id': new_lead.th_origin_id.id
                }

                # Thêm thông báo vào chatter
                msg = _("Cơ hội %s đã được đánh dấu là trùng với cơ hội %s, nhưng không có điều kiện xử lý phù hợp.") % (
                    new_lead.name, old_lead.name)
                new_lead.message_post(body=msg)
                # new_lead.message_post(body=msg)
                
            # Kiểm tra và tạo bản ghi lịch sử nếu chưa tồn tại
            if vals_history and not self.env['th.apm.duplicate.check.history'].search(
                    [('th_lead_apm_id_old', '=', old_lead.id), ('th_lead_apm_id_new', '=', new_lead.id)]):
                self.env['th.apm.duplicate.check.history'].create(vals_history)
                
            return True
        except Exception as e:
            return False

    def _compute_sale_order_count(self):
        for rec in self:
            rec.th_sale_order_count = True if len(
                self.env['sale.order'].search([('th_apm_id', '=', rec.id)])) > 0 else False

    @api.depends('th_partner_id', 'th_origin_id')
    def _compute_partner_values(self):
        for rec in self:
            rec.th_apm_lead_trait_ids = False
            list_trait_ids = []
            if not rec.th_origin_id:
                trait_ids = self.th_partner_id.th_apm_contact_trait_ids
            else:
                trait_ids = self.th_partner_id.th_apm_contact_trait_ids.filtered(
                    lambda lam: lam.th_origin_id == rec.th_origin_id)
            for trait in trait_ids:
                trait_id = self.env['th.apm.lead.trait'].create({
                    'th_origin_id': trait.th_origin_id.id,
                    'th_apm_trait_id': trait.th_apm_trait_id.id,
                    'th_apm_trait_value_ids': [(6, 0, trait.th_apm_trait_value_ids.ids)],
                    'th_apm_contact_trait_id': trait.id,
                    'th_apm_lead_id': rec.id
                })
                list_trait_ids.append(trait_id.id)
                rec.th_apm_lead_trait_ids = [(6, 0, list_trait_ids)]

    def action_create_order(self):
        for rec in self:
            if not rec.th_product_ids:
                raise ValidationError('Không có sản phẩm để tạo đơn hàng!')

            order_line = []
            domain = json.dumps(
                [('id', 'in', rec.th_product_ids.ids)] + [('sale_ok', '=', True), '|', ('company_id', '=', False),
                                                          ('company_id', '=', self.env.company.id)])
            for product_id in rec.th_product_ids:
                order_line.append((0, 0, {
                    'product_id': product_id.id,
                    'product_uom_qty': 1,
                    'price_unit': product_id.list_price,
                    'th_domain_line': domain
                }))
        context = {'default_partner_id': self.th_partner_id.id,
                        'default_order_line': order_line,
                        'default_th_customer_code_aum': self.th_partner_id.th_customer_code,
                        'default_th_apm_id': self.id,
                        'default_th_customer_phone': self.th_partner_phone,
                        'default_th_customer_email': self.th_partner_email,
                        'default_th_domain': domain,
                        'default_th_apm_team_id': self.th_apm_team_id.id,
                        'default_th_introducer_id': self.th_partner_reference_id.id,
                        'default_th_sale_order': 'apm',
                        'default_th_ownership_unit_id':  self.th_ownership_unit_id.id,
                        'default_th_origin_id':  self.th_origin_id.id,
                        'th_domain': domain,
                        'order_create_from_apm': True,  # Check the order conditions created from apm
                        }
        if 'th_is_short_pricelist' in self.env['product.pricelist']._fields:
            if self.env['product.pricelist'].sudo().search([('th_is_short_pricelist', '=', True)]):
                context['default_pricelist_id'] = self.env['product.pricelist'].sudo().search([('th_is_short_pricelist', '=', 'True')]).id

        return {
            'name': _('Đơn hàng'),
            'view_mode': 'form',
            'res_model': 'sale.order',
            'views': [[self.env.ref('th_apm.th_view_order_form').id, 'form']],
            'target': 'new',
            'type': 'ir.actions.act_window',
            'context': context
        }

    def write(self, values):

        if self.th_order_id and values.get('th_user_id') == False:
            values.pop('th_user_id', None)
        # if self._context.get('updated'):
        #     for rec in self:
        #         rec.activity_schedule('th_apm.th_apm_icon', note=note,
        #                       user_id=holiday.sudo()._get_responsible_for_approval().id or self.env.user.id)
        if 'th_partner_id' in values:
            for rec in self:
                rec.th_old_partner_id = rec.th_partner_id
        if 'th_status_detail_id' in values and values.get('th_status_detail_id') and not self._context.get(
                'import_file'):
            values['th_last_check'] = fields.Datetime.today()
        # if 'th_student_status_ids' in values:
        #     log_note = _("%(old_value)s --> %(new_value)s", old_value=self.th_student_status_ids, new_value=values.get('th_student_status_ids'))
        #     self.message_post(body=log_note)
        # if values.get('th_partner_reference_id', ''):
        #     values['th_check_partner_referred'] = True
        if values.get('th_origin_id'):
            rec_apm = self.env['th.apm'].search([('id', '=', self.id)]).name_id_sequence
            th_origin_name = self.env['th.origin'].browse(values.get('th_origin_id'))
            values['name'] = "[%s]" % rec_apm + "-%s-%s" % (self.th_partner_id.display_name,
                                                            th_origin_name.display_name)
        # Ngăn chặn đối tác sửa các trường trên field nhưng vẫn cho log note, if để phân biệt create với write
        # if 'name_id_sequence' not in values and 'name' not in values:
        #     for rec in self:
        #         if rec.th_ownership_unit_id.th_type == "other" and rec.env.user.has_group(
        #                 'th_apm.apm_group_partner_manager') and not rec.env.user.has_group(
        #                 'th_apm.group_apm_administrator'):
        #             return True

        # Kiểm tra cơ hội trùng khi thay đổi khách hàng, dòng sản phẩm, mối quan hệ hoặc trạng thái chi tiết
        # Khi thay đổi th_stage_id hoặc th_status_detail_id, cần kiểm tra lại vì các điều kiện kiểm tra trùng
        duplicate_leads = {}
        if (values.get('th_partner_id') or values.get('th_origin_id') or values.get('th_stage_id') or values.get('th_status_detail_id')) and not self._context.get('th_after_sales_care', False) and not self._context.get('apm_create', False):
            for rec in self:
                # Xác định giá trị khách hàng và dòng sản phẩm sau khi cập nhật
                partner_id = values.get('th_partner_id', rec.th_partner_id.id)
                origin_id = values.get('th_origin_id', rec.th_origin_id.id)

                # Chỉ kiểm tra nếu dòng sản phẩm có bật tính năng kiểm tra trùng
                if origin_id and self.env['th.origin'].search([('th_use_duplicate_check', '=', True), ('id', '=', origin_id)]) and not self._context.get('is_after_order', False):
                    # Tìm cơ hội đã tồn tại với cùng khách hàng và dòng sản phẩm
                    exist_lead = self.env['th.apm'].sudo().search([
                        ('id', '!=', rec.id),
                        ('th_partner_id', '=', partner_id),
                        ('th_origin_id', '=', origin_id),
                        ('th_is_a_duplicate_opportunity', '=', False),
                        ('active', '=', True),
                        ('th_after_sales_care', '=', False),
                        ('th_reason', '=', False)
                    ], limit=1)

                    if exist_lead:
                        duplicate_leads[rec.id] = exist_lead.id

        res = super(APMLead, self).write(values)

        # Xử lý cơ hội trùng sau khi cập nhật
        for rec_id, exist_lead_id in duplicate_leads.items():
            rec = self.browse(rec_id)
            exist_lead = self.browse(exist_lead_id)
            if rec.exists() and exist_lead.exists():
                # Xử lý cơ hội trùng
                rec.with_context(apm_create=True).th_check_condition_lead(exist_lead, rec)

                # Gửi thông báo cho người dùng
                msg = _("Cơ hội này đã trùng với cơ hội %s, \n\n Số điện thoại: %s") % (
                    exist_lead.name, exist_lead.th_partner_id.phone or '')
                self.env['bus.bus'].sudo()._sendone(
                    (self._cr.dbname, 'res.partner', self.env.user.partner_id.id),
                    'simple_notification',
                    {
                        'title': _('Cảnh báo'),
                        'message': msg,
                        'error': True
                    }
                )

        if 'th_partner_id' in values:
            apm_module = self.env.ref('th_setup_parameters.th_apm_module')
            for rec in self:
                rec.th_partner_id.th_module_ids = [(4, apm_module.id)]
                if rec.th_old_partner_id and not len(rec.th_old_partner_id.th_apm_lead_ids):
                    rec.th_old_partner_id.th_module_ids = [(3, apm_module.id)]
        # if values.get('th_apm_lead_trait_ids', False):
        #     partner = self.env['res.partner'].browse(self.th_partner_id.id)
        #     # lead_trait = self.env['th.apm.lead.trait'].browse(values.get('th_apm_lead_trait_ids', False)[0][1])
        #     for rec in values.get('th_apm_lead_trait_ids', False):
        #         params = rec[2]
        #         if params:
        #             all_trait =self.th_partner_id.th_apm_contact_trait_ids.mapped('th_apm_trait_id').ids
        #             if all_trait and params['th_apm_trait_id'] in all_trait:
        #                 raise ValidationError(_('Trùng đặc điểm'))
        #             th_contact_trait_id = self.env['th.apm.contact.trait'].create({
        #                 'th_origin_id': params['th_origin_id'] if params['th_origin_id'] else False,
        #                 'th_apm_trait_id': params['th_apm_trait_id'],
        #                 'th_apm_trait_value_ids': params['th_apm_trait_value_ids']
        #             }).id
        #             partner.with_context(apm_id=self.id).write(
        #                 {'th_apm_contact_trait_ids': [(4, th_contact_trait_id)]})
        if self._context.get('write_one'):
            return res
        # if values.get('th_student_status_ids', False):
        #     for rec in values.get('th_student_status_ids', False):
        #         if rec[2]:
        #             th_student_status = self.env['th.apm.student.status'].search([('id', '=', rec[1])])
        #             th_student_status.write(rec[2])


        if values.get('th_apm_lead_trait_ids', False):
            for rec in self:
                if rec.th_reason and not rec._context.get('import_crm'):
                    raise ValidationError(_('Đã ngừng chăm sóc cơ hội, vui lòng không thay đổi dữ liệu!'))
            for rec in self.th_apm_lead_trait_ids:
                if rec.th_apm_contact_trait_id:
                    rec.th_apm_contact_trait_id.write({
                        'th_origin_id': rec.th_origin_id.id,
                        'th_apm_trait_id': rec.th_apm_trait_id.id,
                        'th_apm_trait_value_ids': [(4, id) for id in rec.th_apm_trait_value_ids.ids]
                    })
                else:
                    th_contact_trait_id = self.env['th.apm.contact.trait'].create({
                        'th_origin_id': rec.th_origin_id.id,
                        'th_apm_trait_id': rec.th_apm_trait_id.id,
                        'th_apm_trait_value_ids': [(6, 0, rec.th_apm_trait_value_ids.ids)]
                    }).id
                    rec.write({'th_apm_contact_trait_id': th_contact_trait_id})
                    self.th_partner_id.with_context(apm_id=self.id).write(
                        {'th_apm_contact_trait_ids': [(4, th_contact_trait_id)]})
        # Chỉ tự động gán th_user_id nếu không phải do thay đổi chiến dịch và chia thủ công
        if values.get('th_apm_team_id') and not self._context.get('from_assign_leads') and not values.get('th_campaign_id') :
            for rec in self:
                rec.th_user_id = rec.th_apm_team_id.with_context(division_type='automatic').action_assign_leads()[rec.th_apm_team_id.id].id
        if values.get('th_ownership_unit_id') and self.th_order_id:
            for rec in self.th_order_id:
                rec.sudo().write({'th_ownership_unit_id': values.get('th_ownership_unit_id')})
        return res

    @api.model
    def create(self, vals_list):
        if self._context.get('import_file'):
            if 'th_campaign_id' not in vals_list or not vals_list.get('th_campaign_id'):
                raise ValueError(
                    _("Trường 'Chiến dịch' bị thiếu hoặc trống trong dữ liệu đã nhập."))
            if vals_list['th_origin_id'] and (not self._context.get('web_form') or not self._context.get('lead_auto')):
                th_product_line = self.env['th.apm.campaign'].search(
                    [('id', '=', vals_list['th_campaign_id'])])
                if th_product_line.th_origin_id.id != vals_list['th_origin_id'] and vals_list['th_campaign_id'] == self.env.ref('th_apm.campaign_lead_auto').id:
                    vals_list['th_origin_id'] = self.env.ref('th_setup_parameters.th_origin_vmc').id
                elif th_product_line.th_origin_id.id != vals_list['th_origin_id'] and vals_list['th_campaign_id'] != self.env.ref('th_apm.campaign_lead_auto').id:
                    raise ValueError("Dòng sản phẩm không thuộc chiến dịch")
            if self._context.get('import_file') and (vals_list.get('th_import_phone',False) or vals_list.get('th_import_email',False)):
                partner_phone = False
                partner_email = False
                module = self.env.ref('th_setup_parameters.th_apm_module')
                check_module = self.env['ir.config_parameter'].sudo().get_param('th_check_partner_module')
                if vals_list.get('th_import_phone', False):
                    domain = ['|', ('phone', '=', vals_list['th_import_phone']), ('th_phone2', '=', vals_list['th_import_phone']),]
                    if check_module:
                        domain.append(('th_module_ids', 'in', module.ids))
                    partner_phone = self.env['res.partner'].sudo().search(domain, limit=1)
                    if partner_phone:
                        vals_list.update({
                            'th_partner_id': partner_phone.id
                        })
                if not partner_phone and vals_list.get('th_import_email', False):
                    domain = [('email', '=', vals_list['th_import_email']), ]
                    if check_module:
                        domain.append(('th_module_ids', 'in', module.ids))
                    partner_email = self.env['res.partner'].sudo().search(domain, limit=1)
                    if partner_email:
                        vals_list.update({
                            'th_partner_id': partner_email.id,
                        })
                if not partner_phone and not partner_email:
                    partner = self.sudo().env['res.partner'].sudo().create({'name': vals_list.get('name_import', False),
                                                                            'phone': vals_list.get('th_import_phone', False),
                                                                            'email': vals_list.get('th_import_email', False),
                                                                            'th_module_ids':[(4, module.id)]})

                    vals_list.update({
                        'th_partner_id': partner.id,
                    })

        # Kiểm tra cơ hội trùng khi tạo mới
        if vals_list.get('th_partner_id') and vals_list.get('th_origin_id') and not self._context.get('th_after_sales_care', False):
            if self.env['th.origin'].search([('th_use_duplicate_check', '=', True),('id', '=', vals_list.get('th_origin_id'))]):
                # Tìm cơ hội đã tồn tại với cùng khách hàng và dòng sản phẩm
                exist_lead = self.env['th.apm'].sudo().search(
                    [('th_partner_id', '=', vals_list.get('th_partner_id')),
                    ('th_origin_id', '=', vals_list.get('th_origin_id')),
                    ('th_is_a_duplicate_opportunity', '=', False),
                    ('active', '=', True),
                    ('th_after_sales_care', '=', False),
                    ('th_reason', '=', False)], limit=1)

                if exist_lead:
                    # Đánh dấu trong context để sử dụng sau khi tạo
                    self = self.with_context(duplicate_lead=exist_lead.id)
        if not vals_list.get('th_customer_code_aum'):
            vals_list['th_customer_code_aum'] = self.env['res.partner'].search([('id', '=', vals_list.get('th_partner_id'))]).th_customer_code
        # Thêm logic cập nhật kênh từ cơ hội trước bán
        if vals_list.get('th_after_sales_care') and vals_list.get('th_partner_id'):
            # Tìm cơ hội có đơn hàng của khách hàng
            opportunities = self.env['th.apm'].search([
                ('th_partner_id', '=', vals_list['th_partner_id']),
                ('th_order_id', '!=', False),
                ('th_after_sales_care', '=', False)
            ])

            # Nếu có cơ hội có đơn hàng, kiểm tra kênh
            if opportunities:
                channels = opportunities.mapped('th_channel_id')
                if len(channels) == 1:
                    # Nếu chỉ có 1 kênh duy nhất
                    vals_list['th_channel_id'] = channels.id
                else:
                    # Nếu có nhiều kênh khác nhau, để trống để người dùng chọn
                    vals_list['th_channel_id'] = False
        # if self._context.get('aff_apm_lead'):
            # vals_list = self.create_lead_aff(vals_list)
            # vals_list['th_status_category_id'] = self.env.ref('th_apm.th_no_process_category').id
            # vals_list['th_status_detail_id'] = self.env.ref('th_apm.th_no_process_detail').id
        if self._context.get('web_form'):
            vals_list['th_campaign_id'] = self.env.ref('th_apm.campaign_lead_formio').id
            vals_list['th_status_category_id'] = self.env.ref('th_apm.th_no_process_category').id
            vals_list['th_status_detail_id'] = self.env.ref('th_apm.th_no_process_detail').id
        # if vals_list.get('th_partner_reference_id', False):
        #     vals_list['th_check_partner_referred'] = True
        # if vals_list.get('th_partner_id', False):
        #     partner = self.env['res.partner'].browse(vals_list.get('th_partner_id', False))
        #     vals_list['th_apm_phone'] = partner.phone
        if vals_list.get('th_student_status_ids', False):
            pass
        if vals_list.get('th_apm_lead_trait_ids', False):
            partner = self.env['res.partner'].browse(vals_list.get('th_partner_id', False))
            for rec in vals_list.get('th_apm_lead_trait_ids', False):
                params = rec[2]
                if params:
                    th_contact_trait_id = self.env['th.apm.contact.trait'].create({
                        'th_origin_id': params['th_origin_id'],
                        'th_apm_trait_id': params['th_apm_trait_id'],
                        'th_apm_trait_value_ids': [(6, 0, params['th_apm_trait_value_ids'][0][2])]
                    }).id
                    partner.with_context(apm_id=self.id).write(
                        {'th_apm_contact_trait_ids': [(4, th_contact_trait_id)]})
        res = super(APMLead, self).create(vals_list)
        for rec in res:
            rec.th_partner_id.write({'th_module_ids': [(4, self.env.ref('th_setup_parameters.th_apm_module').id)]})
        if self._context.get('import_file'):
            if self._context.get('name_create_enabled_fields').get('th_partner_id', False):
                res.th_partner_id.write({
                    'phone': vals_list['th_apm_phone'],
                    'email': vals_list['th_apm_email']
                })

        if res.th_apm_team_id and (self._context.get('aff_apm_lead') or not res.th_user_id):
            res.th_user_id = res.th_apm_team_id.with_context(division_type=res.th_campaign_id.th_divide).action_assign_leads()[vals_list['th_apm_team_id']].id

        name_id = self.env['ir.sequence'].next_by_code('th.apm')
        if res.name == 'MỚI':
            res.name_id_sequence = name_id
            if res.th_campaign_id.th_origin_id.display_name:
                res.name = "[" + name_id + "]" + "-" + res.th_partner_id.display_name + "-" + res.th_campaign_id.th_origin_id.display_name
            else:
                res.name = "[" + name_id + "]" + "-" + res.th_partner_id.display_name

        res.th_partner_id.write({
            'th_module_ids': [(4, self.env.ref('th_setup_parameters.th_apm_module').id)]
        })

        # Xử lý cơ hội trùng sau khi tạo
        if self._context.get('duplicate_lead'):
            exist_lead = self.env['th.apm'].browse(self._context.get('duplicate_lead'))
            if exist_lead and exist_lead.exists():
                # Xử lý cơ hội trùng
                res.th_check_condition_lead(exist_lead, res)

                # Gửi thông báo cho người dùng
                msg = _("Cơ hội này đã trùng với cơ hội %s, \n\n Số điện thoại: %s") % (
                    exist_lead.name, exist_lead.th_partner_id.phone or '')
                self.env['bus.bus'].sudo()._sendone(
                    (self._cr.dbname, 'res.partner', self.env.user.partner_id.id),
                    'simple_notification',
                    {
                        'title': _('Cảnh báo'),
                        'message': msg,
                        'error': True
                    }
                )
        # tắt đồng bộ
        # if vals_list.get('th_partner_reference_id', False) and not self._context.get(
        #         'aff_apm_lead') and not self._context.get('th_test_import', False):
        #     self.check_synchronization([], res)
        # contact_trait = []
        for rec in res.th_apm_lead_trait_ids:
            exit_contact_trait = False
            if not rec.th_apm_contact_trait_id:
                exit_contact_trait = res.th_partner_id.th_apm_contact_trait_ids.filtered(lambda l:
                                                                                         l.th_origin_id.id == rec.th_origin_id.id and
                                                                                         l.th_apm_trait_id.id == rec.th_apm_trait_id.id)
                if exit_contact_trait:
                    exit_contact_trait.write({
                        'th_apm_trait_value_ids': [(4, trait.id) for trait in rec.th_apm_trait_value_ids]
                    })
                else:
                    th_contact_trait_id = self.env['th.apm.contact.trait'].create({
                        'th_origin_id': rec.th_origin_id.id,
                        'th_apm_trait_id': rec.th_apm_trait_id.id,
                        'th_apm_trait_value_ids': [(6, 0, rec.th_apm_trait_value_ids.ids)]
                    }).id
                    res.th_partner_id.with_context(apm_id=res.id).sudo().write(
                        {'th_apm_contact_trait_ids': [(4, th_contact_trait_id)]})
                rec.write(
                    {'th_apm_contact_trait_id': th_contact_trait_id if not exit_contact_trait else exit_contact_trait})
            else:
                rec.th_apm_contact_trait_id.sudo().write({
                    'th_origin_id': rec.th_origin_id.id,
                    'th_apm_trait_id': rec.th_apm_trait_id.id,
                    'th_apm_trait_value_ids': [(6, 0, rec.th_apm_trait_value_ids.ids)]
                })
        return res

    def create_lead_aff(self, vals):
        th_origin = False
        th_source_group_id = False
        th_partner_referred_id = False
        th_channel_id = self.env.ref('th_setup_parameters.th_channel_unknown').id
        th_ownership = self.env['th.ownership.unit'].search([('th_code', '=', self._context.get('th_ownership_code'))], limit=1)
        check_module = self.env['ir.config_parameter'].sudo().get_param('th_check_partner_module')
        if vals.get('th_warehouse_code', False):
            th_origin = self.env['th.origin'].search([('th_code', '=', vals.get('th_warehouse_code'))], limit=1)
        if vals.get('th_utm_source', False):
            th_source_group_id = self.env['th.source.group'].search([('name', '=', vals.get('th_utm_source', False))], limit=1).id
            if not th_source_group_id:
                th_source_group_id = self.env['th.source.group'].create({
                    'name': vals.get('th_utm_source', False),
                }).id

        if vals.get('th_affiliate_code', False):
            th_partner_referred_id = self.env['res.partner'].search([('th_affiliate_code', '=', vals.get('th_affiliate_code'))], limit=1)
        domain = []
        if vals.get('th_phone', False):
            domain = ['|', ('phone', '=', vals.get('th_phone')), ('th_phone2', '=', vals.get('th_phone'))]
        if vals.get('th_email', False):
            domain = ['|', ] + domain + [('email', '=', vals.get('th_email'))] if domain else [('email', '=', vals.get('th_email'))]
        if check_module:
            domain.append(('th_module_ids', 'in', self.env.ref('th_setup_parameters.th_apm_module').ids))
        partner_aff = False
        if domain:
            partner_aff = self.env['res.partner'].search(domain, limit=1)
            if partner_aff.phone and not partner_aff.th_phone2 and vals.get('th_phone', False) and partner_aff.phone != vals.get('th_phone', False):
                partner_aff.write({
                    'th_phone2': vals.get('th_phone'),
                })
            elif not partner_aff.phone and vals.get('th_phone', False):
                partner_aff.write({
                    'phone': vals.get('th_phone'),
                })
            elif partner_aff.th_phone2 and partner_aff.phone and vals.get('th_phone', False) and vals.get(
                    'th_phone', False) not in [partner_aff.th_phone2, partner_aff.phone]:
                partner_aff.write({
                    'comment': Markup(
                        partner_aff.comment + 'Số điện thoại mới: ' + vals.get('th_phone', False)) if partner_aff.comment else Markup(
                        'Số điện thoại mới: ' + vals.get('th_phone', False))
                })

        campaign = self.env['th.apm.campaign'].search([('id', '=', self.env.ref('th_apm.campaign_lead_formio').id)], limit=1)
        if not partner_aff:
            partner_aff = self.env['res.partner'].create({
                'name': vals['th_customer'] if vals.get('th_customer') else vals.get('th_phone'),
                'phone': vals.get('th_phone', False),
                'email': vals.get('th_email', False),
                'th_module_ids': [(4, self.env.ref('th_setup_parameters.th_apm_module').id)],
            })

        new_opportunity = {
            'th_opportunity_aff_id': vals.get('id', False),
            'th_partner_id': partner_aff.id,
            'th_campaign_id': campaign.id if campaign else False,
            'th_apm_email': vals.get('th_email', False),
            'th_apm_phone': vals.get('th_phone', False),
            'th_origin_id': th_origin.id if th_origin else False,
            'th_partner_reference_id': th_partner_referred_id.id if th_partner_referred_id else False,
            'th_ownership_unit_id': th_ownership.id if th_ownership else False,
            'th_description': vals.get('th_description', False),
            'th_status_category_id': self.env.ref('th_apm.th_no_process_category').id,
            'th_utm_source': vals.get('th_utm_source', False),
            'th_utm_medium': vals.get('th_utm_medium', False),
            'th_utm_campaign': vals.get('th_utm_campaign', False),
            'th_utm_term': vals.get('th_utm_term', False),
            'th_utm_content': vals.get('th_utm_content', False),
            'th_source_group_id': th_source_group_id,
            'th_channel_id': th_channel_id,
        }
        if self._context.get('th_form_id') and self._context.get('aff_apm_lead'):
            th_formio_pro_id = self.env['th.formio.builder.field.aff.default'].search(
                [('th_uuid', '=', self._context.get('th_form_id'))],limit=1)
            if th_formio_pro_id:
            #     th_formio_pro_id.th_origin_id = th_origin.id if th_origin else False
            #     th_formio_pro_id.th_ownership_unit_id = th_ownership.id if th_ownership else False
                new_opportunity.update({
                    # 'th_origin_id': th_formio_pro_id.th_origin_id.id if th_formio_pro_id.th_origin_id else False,
                    # 'th_ownership_unit_id': th_formio_pro_id.th_ownership_unit_id.id if th_formio_pro_id.th_ownership_unit_id else False,
                    'th_apm_team_id': th_formio_pro_id.action_assign_leads_apm(),
                    'th_apm_dividing_ring_id': th_formio_pro_id.th_apm_dividing_ring_id.id if th_formio_pro_id.th_apm_dividing_ring_id else False,
                })
                # Gán người chăm sóc theo vòng chia nếu có
                if th_formio_pro_id.th_apm_dividing_ring_id:
                    user_id = th_formio_pro_id.th_apm_dividing_ring_id.action_assign_leads_dividing_ring()
                    if user_id:
                        new_opportunity.update({
                            'th_user_id': user_id,
                        })

        try:
            apm_lead = self.sudo().create(new_opportunity)
        except Exception as e:
            print(e)

        if vals.get('id', False) and apm_lead:
            exist_lead = False
            # for rec in apm_lead:
            #     exist_lead = self.env['crm.lead'].search(
            #         [('id', '!=', rec.id), ('th_origin_id', '=', rec.th_origin_id.id),
            #          ('partner_id', '=', rec.partner_id.id), ('th_is_a_duplicate_opportunity', '=', False)], limit=1)
            return {'apm_id': apm_lead.id, 'duplicate': False if not exist_lead else True}
        return {}

    def action_view_sale_order(self):
        self.ensure_one()
        return {
            'type': 'ir.actions.act_window',
            'name': 'Đơn hàng',
            'view_mode': 'tree,form',
            # 'view_id': False,
            'res_model': 'sale.order',
            'domain': [('partner_id', '=', self.th_partner_id.id), ('th_apm_id', '=', self.id)],
            'views': [(self.env.ref('th_apm.view_order_tree').id, "tree")],
            'context': {'create': False, },
            # 'domain': [('th_apm_id', '=', self.id)],
            # 'context': {'create': False,'view_apm':True }
        }

    def th_action_view_complaints(self):
        """Hiển thị danh sách khiếu nại của cơ hội"""
        self.ensure_one()
        return {
            'type': 'ir.actions.act_window',
            'name': _('Khiếu nại cơ hội trùng'),
            'view_mode': 'tree,form',
            'res_model': 'th.apm.duplicate.complaint',
            'domain': [('th_apm_id', '=', self.id)],
            'context': {'default_th_apm_id': self.id},
        }

    def th_action_list_duplicate(self):
        """Hiển thị danh sách cơ hội trùng"""
        th_history = self.env['th.apm.duplicate.check.history'].search([
            ('th_lead_apm_id_new', '=', self.id), 
            ('th_duplicate_type', '=', 'need_handle')
        ]).filtered(lambda x: x.th_lead_apm_id_old.th_is_under_complaint)
        
        return {
            "name": _("Phân xử"),
            "type": 'ir.actions.act_window',
            "res_model": 'th.apm.duplicate.check.history',
            "views": [[False, "tree"], [False, "form"]],
            'view_id': self.env.ref('th_apm.th_apm_duplicate_tree_view').id,
            "target": 'new',
            "context": {},
            "domain": [('id', 'in', th_history.ids)],
        }


    def th_action_complaint(self):
        """Mở wizard khiếu nại cơ hội trùng"""
        self.ensure_one()

        # Kiểm tra điều kiện
        if not self.th_is_a_duplicate_opportunity:
            raise ValidationError(_('Chỉ có thể khiếu nại cơ hội đã bị đánh dấu trùng!'))

        if self.th_dup_need_admin:
            raise ValidationError(_('Cơ hội này đã có khiếu nại đang chờ xử lý!'))

        if self.th_is_under_complaint:
            raise ValidationError(_('Cơ hội này đã có khiếu nại đang chờ xử lý!'))

        # Mở wizard khiếu nại
        return {
            'name': _('Khiếu nại cơ hội trùng'),
            'type': 'ir.actions.act_window',
            'view_mode': 'form',
            'res_model': 'th.apm.duplicate.complaint.wizard',
            'target': 'new',
            'context': {'default_th_apm_id': self.id},
        }

    def th_action_keep_opportunity(self):
        """Phê duyệt khiếu nại và giữ lại cơ hội đang chăm hiện tại"""
        self.ensure_one()

        # Kiểm tra điều kiện
        if self.th_is_a_duplicate_opportunity:
            raise ValidationError(_('Chỉ có thể thực hiện với cơ hội đang chăm sóc hiện tại!'))

        if not self.th_is_under_complaint:
            raise ValidationError(_('Cơ hội này không có khiếu nại đang chờ xử lý!'))

        # Tìm lịch sử kiểm tra trùng
        th_history = self.env['th.apm.duplicate.check.history'].search([
            ('th_lead_apm_id_new', '=', self.id), 
            ('th_duplicate_type', '=', 'need_handle')
        ]).filtered(lambda x: x.th_lead_apm_id_old.th_is_under_complaint)
        if not th_history:
            raise ValidationError(_('Không tìm thấy cơ hội trùng!'))
        
        for history in th_history:
            # Bỏ đánh dấu cơ trùng đang khiếu nại
            history.th_lead_apm_id_old.sudo().write({
                'th_is_under_complaint': False,
                'th_dup_need_admin': False,
            })
            history.sudo().write({
                'th_duplicate_type': 'manual',
            })
        # Bỏ trạng thái khiếu nại cho cơ hội thắng
        self.sudo().write({
            'th_dup_need_admin': False,
            'th_is_under_complaint': False,
            'th_duplicate_type': 'auto',
        })
        
        # Gửi notification qua bus
        self.env['bus.bus'].sudo()._sendone(
            (self._cr.dbname, 'res.partner', self.env.user.partner_id.id),
            'simple_notification',
            {
                'title': _('Thành công'),
                'message': _('Đã phê duyệt khiếu nại thành công!'),
                'type': 'success',
                'sticky': False
            }
        )
    
        return self.env['ir.actions.act_window']._for_xml_id('th_apm.th_action_apm_complaint_duplicate')
        
    def th_action_close_opportunity(self):
        for rec in self:
            rec.th_is_close_lead = True
            msg = ("Cơ hội đã bị đóng, vui lòng không chỉnh sửa thêm!")
            rec.message_post(body=msg)

    def th_action_open_opportunity(self):
        for rec in self:
            rec.th_is_close_lead = False
            msg = ("Đã mở lại cơ hội")
            rec.message_post(body=msg)


    @api.model
    def action_th_product_template(self):
        domain = json.dumps([('id', 'in', self.env['product.category'].search(
            [('th_module_ids', 'in', self.env.ref('th_setup_parameters.th_apm_module').ids)]).ids)])
        action = self.env['ir.actions.actions']._for_xml_id('th_apm.th_product_template_action_apm')
        action['domain'] = [('categ_id', 'in', self.env['product.category'].search(
            [('th_module_ids', 'in', self.env.ref('th_setup_parameters.th_apm_module').ids)]).ids)]
        action['context'] = {
            'default_th_product_category_domain_1': domain, "search_default_filter_to_sell": 1,
            'view_product_apm': True, 'default_categ_id': False}
        return action

    def _compute_sale_order_count1(self):
        for rec in self:
            rec.sale_order_count = len(
                self.env['sale.order'].search([('partner_id', '=', rec.th_partner_id.id), ('th_apm_id', '=', self.id)]))

    def get_data_opportunity_ctv(self, rec_apm):
        data = {}
        for rec in rec_apm:
            data['th_lead_id_samp'] = rec.id
            if rec.name:
                data['name'] = rec.name
            if rec.th_partner_reference_id:
                data['th_affiliate_code'] = rec.th_partner_reference_id.th_affiliate_code
            if rec.th_partner_id:
                data['th_customer'] = rec.th_partner_id.name
                data['th_customer_code'] = rec.th_partner_id.th_customer_code
            if rec.th_description:
                data['th_description'] = rec.th_description
            if rec.th_product_category_id:
                data['th_product_category'] = rec.th_product_category_id.name
            if rec.th_status_category_id:
                data['th_status_category'] = rec.th_status_category_id.name
            if rec.th_status_detail_id:
                data['th_status_detail'] = rec.th_status_detail_id.name
            if rec.th_stage_id:
                data['th_stage'] = rec.th_stage_id.name
            if rec.th_product_ids:
                data['th_products'] = ', '.join(rec.th_product_ids.mapped('name'))
            if rec.th_reason:
                data['th_reason'] = rec.th_reason
            if rec.th_user_id:
                data['th_caregiver'] = rec.th_user_id.name
        return data

    def check_synchronization(self, data_code_aff=[], res=None):
        data_to_send = {}
        data_from_aff = self.search([('th_affiliate_code', 'in', data_code_aff)])
        if res:
            data_from_aff = res
        try:
            server_api = self.env['th.api.server'].search([('state', '=', 'deploy'), ('th_type', '=', 'aff')], limit=1, order='id desc')
            result_apis = xmlrpc.client.ServerProxy('{}/xmlrpc/2/object'.format(server_api.th_url_api))
            db = server_api.th_db_api
            uid_api = server_api.th_uid_api
            password = server_api.th_password
            for rec in data_from_aff:
                context = {'module': self.env.ref('th_setup_parameters.th_apm_module').name,
                           'origin': rec.th_origin_id.th_code,
                           'company_code': rec.th_ownership_unit_id.th_code,
                           'th_create': True if not rec.th_opportunity_aff_id else False,
                           'th_opportunity_aff_id': [rec.th_opportunity_aff_id] if rec.th_opportunity_aff_id else [],
                           }
                data_to_send = self.get_data_opportunity_ctv(rec)
                res_id = result_apis.execute_kw(db, uid_api, password, 'th.opportunity.ctv', 'receive_data',
                                                [[], data_to_send], {'context': context})
                if res_id.get('id', False):
                    rec.write({'th_opportunity_aff_id': int(res_id.get('id', False))})
            return True
        except Exception as e:
            print(e)
            self.env['th.log.api'].create({
                'state': 'error',
                'th_model': str(self._name),
                'th_description': str(e),
                'th_record_id': str(res.ids),
                'th_input_data': str(data_to_send),
                'th_function_call': 'check_synchronization',
            })
            return False

    @api.depends('th_apm_dividing_ring_id')
    def _compute_th_user_id_domain(self):
        for rec in self:
            domain = []
            # Logic domain dựa theo th_apm_dividing_ring_id
            if rec.th_apm_dividing_ring_id:
                # Lấy danh sách user_ids từ vòng chia
                user_ids = rec.th_apm_dividing_ring_id.th_user_ids.ids
                if user_ids:
                    domain.append(('id', 'in', user_ids))
                    rec.th_user_id_domain = json.dumps(domain)
                else:
                    rec.th_user_id_domain = json.dumps(domain)
            else:
                rec.th_user_id_domain = json.dumps(domain)
            # if rec.th_apm_team_id and rec.th_user_id.id not in rec.th_apm_team_id.th_member_ids.ids + rec.th_apm_team_id.manager_id.ids:
            #     self.sudo().th_user_id = False
            #     user_ids = self.env['th.apm.team'].search([('id', '=', rec.th_apm_team_id.id)]).th_member_ids.ids
            #     domain.append(('id', 'in', user_ids))
            #     rec.th_user_id_domain = json.dumps(domain)
            # elif rec.th_apm_team_id and rec.th_user_id.id in rec.th_apm_team_id.th_member_ids.ids:
            #     user_ids = self.env['th.apm.team'].search([('id', '=', rec.th_apm_team_id.id)]).th_member_ids.ids
            #     domain.append(('id', 'in', user_ids))
            #     rec.th_user_id_domain = json.dumps(domain)
            # else:
            #     rec.th_user_id_domain = json.dumps(domain)

    @api.depends('th_status_category_id')
    def _compute_th_state_detail_domain(self):
        for rec in self:
            domain = []
            if rec.th_status_category_id:
                domain.append(('id', 'in', rec.th_status_category_id.th_status_detail_ids.filtered(
                    lambda s: rec.th_stage_id.id in s.th_apm_level_ids.ids).ids))
            else:
                rec.th_status_detail_id = False
            rec.th_state_detail_domain = json.dumps(domain)

    @api.depends('th_origin_id')
    def _compute_th_product_category_domain(self):
        for rec in self:
            domain = []
            # domain_pro_line = []
            if rec.th_origin_id:
                category_ids = rec.env['product.category'].search([]).filtered(lambda lam:
                                                                               self.env.ref(
                                                                                   'th_setup_parameters.th_apm_module').id in lam.th_module_ids.ids and rec.th_origin_id.id in lam.th_origin_ids.ids).ids
                domain.append(('id', 'in', category_ids))
            else:
                category_ids = rec.env['product.category'].search([]).filtered(lambda lam:
                                                                               self.env.ref(
                                                                                   'th_setup_parameters.th_apm_module').id in lam.th_module_ids.ids).ids
                domain.append(('id', 'in', category_ids))
            rec.th_product_category_domain = json.dumps(domain)
            rec.th_product_line_domain = json.dumps([('id', 'in', [self.env.ref(xml_id).id for xml_id in ORIGIN_LINE])])

    @api.depends('th_product_category_id', 'th_origin_id', 'th_origin_ids')
    def _compute_th_product_domain(self):
        for rec in self:
            domain = []
            if rec.th_product_category_id:
                domain.append(('categ_id', 'in', rec.env['product.category'].search([]).filtered(
                    lambda l: str(rec.th_product_category_id.id) in l.parent_path).ids))
            if rec.th_origin_id and not rec.th_product_category_id:
                domain.append(('categ_id', 'in', rec.env['product.category'].search([]).filtered(lambda lam:
                                                                                                 self.env.ref(
                                                                                                     'th_setup_parameters.th_apm_module').id in lam.th_module_ids.ids and rec.th_origin_id.id in lam.th_origin_ids.ids).ids))
            # if not rec.th_origin_id and not rec.th_product_category_id:
            #     domain.append(('categ_id', 'in', rec.env['product.category'].search([]).filtered(lambda lam:
            #         self.env.ref('th_setup_parameters.th_apm_module').id in lam.th_module_ids.ids).ids))
            if rec.th_origin_ids:
                category = []
                for origin in rec.th_origin_ids.ids:
                    category += rec.env['product.category'].search([]).filtered(lambda lam:
                                                                                self.env.ref(
                                                                                    'th_setup_parameters.th_apm_module').id in lam.th_module_ids.ids and origin in lam.th_origin_ids.ids).ids
                domain.append(('categ_id', 'in', category))

            rec.th_product_domain = json.dumps(domain)

    @api.model
    def get_import_templates(self):
        return [{
            'label': _('Mẫu Import'),
            'template': '/th_apm/static/xls/sample_import_apm1.xlsx'
        }]

    @api.model
    def run_opportunity_sync(self, res=True, campaign=None):
        # chưa dùng tới
        data_to_send = res
        return False

        server_api = self.env['th.api.server'].search([('state', '=', 'deploy'), ('th_type', '=', 'aff')], limit=1,
                                                      order='id desc')
        if not server_api:
            return False
        try:
            result_apis = xmlrpc.client.ServerProxy('{}/xmlrpc/2/object'.format(server_api.th_url_api))
            db = server_api.th_db_api
            uid_api = server_api.th_uid_api
            password = server_api.th_password
            data = result_apis.execute_kw(db, uid_api, password, 'th.opportunity.ctv', 'th_action_synchronize_data',
                                          [[], res])
            start_time = datetime.now().replace(hour=0, minute=0, second=0, microsecond=0) - timedelta(days=1)
            if campaign:
                opportunity_campaign = self.env['th.apm'].search(
                    [('th_campaign_id', '=', campaign.id), ('th_affiliate_code', 'in', data)])
                log_note = self.env['mail.message'].search(
                    [('model', '=', 'th.apm'), ('res_id', 'in', opportunity_campaign.ids)]).filtered(
                    lambda log: log.th_aff_mail_message_id != False)
                affiliate_codes = opportunity_campaign.mapped('th_affiliate_code')
                data1 = self.check_synchronization(data_code_aff=affiliate_codes)
                # self.env['mail.message'].action_synchronized_mail_message(log_note)
            else:
                opportunity_apm = self.env['th.apm'].search(
                    [('write_date', '>=', start_time), ('write_date', '<=', datetime.now()),
                     ('th_affiliate_code', 'in', data)], limit=1)
                log_note = self.env['mail.message'].search([('model', '=', 'th.apm'), ('res_id', 'in', opportunity_apm.ids)]).filtered(
                    lambda log: log.th_aff_mail_message_id == False)
                affiliate_codes = opportunity_apm.mapped('th_affiliate_code')
                data1 = self.check_synchronization(data_code_aff=affiliate_codes)
                # self.env['mail.message'].action_synchronized_mail_message(log_note)
            return data1
        except Exception as e:
            self.env['th.log.api'].create({
                'state': 'error',
                'th_model': str(self._name),
                'th_description': str(e),
                'th_record_id': str(self.ids),
                'th_input_data': str(data_to_send),
                'th_function_call': 'run_opportunity_sync',
            })
            raise ValidationError(e)

    @api.depends('th_stage_id')
    def _compute_th_level_up_date(self):
        for rec in self:
            if not rec.th_stage_id.th_first_status:
                rec.th_level_up_date = fields.Date.today()
            elif rec.th_stage_id.th_first_status:
                rec.th_level_up_date = rec.th_level_up_date

    @api.model
    def th_action_deduplicate_apm(self):
        rec_duplicate = self.env['data_merge.model'].search([('res_model_id.model', '=', 'th.apm')])
        if rec_duplicate:
            for rec in rec_duplicate:
                rec.th_apm_action_find_duplicates()
                action = self.env["ir.actions.actions"]._for_xml_id(
                    "th_apm.th_action_data_merge_record_apm_notification")
                return action
        else:
            raise ValidationError("Đã có lỗi xảy ra. Vui lòng liên hệ Admin để xử lý cấu hình")

    @api.depends('th_order_history_ids')
    def _compute_recent_purchase(self):
        for rec in self:
            sale = self.env['sale.order'].sudo().search(
                        [('partner_id', '=', rec.th_partner_id.id), ('th_apm_id', '!=', False),
                         ('invoice_status', '=', 'invoiced')])
            if rec.th_is_lead_TTVH and len(sale.ids) > 0:
                for order in sale:
                    if not rec.th_day_recent_purchase or order.create_date >= rec.th_day_recent_purchase:
                        rec.th_day_recent_purchase = order.create_date
            else:
                rec.th_day_recent_purchase = False

    @api.depends('th_order_history_ids')
    def _compute_first_purchase(self):
        for rec in self:
            if rec.th_order_history_ids:
                first_purchase = min(rec.th_order_history_ids.mapped('th_create_date'))
                rec.th_first_day_purchase = first_purchase.date()
            else:
                rec.th_first_day_purchase = False

    @api.depends('th_is_lead_TTVH')
    def compute_order_history(self):
        for rec in self:
            rec.th_order_history_ids = False
            if rec.th_is_lead_TTVH:
                his_order_ids = []
                for order in self.env['sale.order'].sudo().search(
                        [('partner_id', '=', rec.th_partner_id.id), ('th_apm_id', '!=', False), ('invoice_status', '=', 'invoiced'),
                         ('th_is_a_simple_lesson', '=', False), ('th_origin_id','=', rec.th_origin_id.id)]):
                    if not any(order.invoice_ids.filtered(lambda d: d.th_payment_status not in ('paid', 'over_payment'))):
                        his_order_id = self.env['th.order.history'].create({
                            'th_ownership_unit_id': order.th_ownership_unit_id.name,
                            'th_ownership_unit_new_id': order.th_ownership_unit_id.id,
                            'user_id': order.user_id.id,
                            'th_sale_order_id': order.id,
                            'th_product_ids': [[6, 0, order.order_line.product_id.ids]]
                        })
                        his_order_ids.append(his_order_id.id)
                rec.th_order_history_ids = [(6, 0, his_order_ids)]



    @api.depends('th_order_history_ids', 'th_student_status_ids')
    def compute_th_student_status(self):
        for rec in self:
            rec.th_check_c = False
            if not rec.th_is_lead_TTVH:
                continue

            # Lấy các đơn hàng đã có trong hệ thống
            orders = self.env['sale.order'].sudo().search([
                ('partner_id', '=', rec.th_partner_id.id),
                ('th_apm_id', '!=', False),
                ('invoice_status', '=', 'invoiced'),
                ('th_is_a_simple_lesson', '=', False),
                ('th_origin_id', '=', rec.th_origin_id.id)
            ])

            # Tìm các đơn hàng đã thanh toán
            paid_orders = orders.filtered(
                lambda o: not any(o.invoice_ids.filtered(
                    lambda d: d.th_payment_status not in ('paid', 'over_payment')
                ))
            )

            # Lấy tất cả các sale_order_id đã có trong student_status
            existing_orders = rec.th_student_status_ids.mapped('th_sale_order_id.id')

            # Chỉ xử lý các đơn hàng mới, chưa có trong th_student_status_ids
            new_orders = paid_orders.filtered(lambda o: o.id not in existing_orders)

            # Thêm các bản ghi mới cho các đơn hàng chưa có
            his_order_ids = []
            for order in new_orders:
                for res in order.order_line.product_id:
                    if res.default_code:
                        his_order_id = self.env['th.apm.student.status'].create({
                            'th_lead_apm_id': rec.id,  # Quan trọng: cần liên kết với record hiện tại
                            'th_ownership_unit_id': order.th_ownership_unit_id.name,
                            'th_sale_order_id': order.id,
                            'th_product_id': res.id,
                            'th_registration_date': order.invoice_ids.invoice_date_due if len(
                                order.invoice_ids) == 1 else False
                        })
                        his_order_ids.append(his_order_id.id)

            # Chỉ thêm các bản ghi mới mà không xoá các bản ghi cũ
            if his_order_ids:
                rec.th_student_status_ids = [(4, id) for id in his_order_ids]

    def action_open_import_student_status(self):
        return {
            'name': _('Import student status'),
            'view_mode': 'form',
            'res_model': 'import.student.status.wizard',
            'view_id': False,
            'type': 'ir.actions.act_window',
            'target': 'new',
        }

    def action_open_apm_partner(self):
        self.ensure_one()
        return {
            'name': 'Khách hàng',
            'view_mode': 'form',
            'res_model': 'res.partner',
            'type': 'ir.actions.act_window',
            'target': 'new',
            'res_id': self.th_partner_id.id,
            'domain': [('id', '=', self.th_partner_id.id)],
        }

    def unlink(self):
        for rec in self:
            if len(rec.th_partner_id.th_apm_lead_ids) <= 1:
                rec.th_partner_id.th_module_ids = [(3, self.env.ref('th_setup_parameters.th_crm_module').id)]
        return super(APMLead, self).unlink()

    def action_export_data(self):
        action = self.env.ref('th_apm.sample_import_student_status')
        return action.report_action(self)
    
