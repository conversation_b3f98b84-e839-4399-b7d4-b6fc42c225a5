<odoo>
    <!-- Wizard Form View -->
    <record id="th_course_report_wizard_form" model="ir.ui.view">
        <field name="name">th.course.report.wizard.form</field>
        <field name="model">th.course.report.wizard</field>
        <field name="arch" type="xml">
            <form string="Báo cáo thống kê khóa học">
                <sheet>
                    <!-- Tiêu chí lọc -->
                    <group string="Tiêu chí lọc báo cáo" invisible="context.get('show_report_result', False) == True">
                        <group>
                            <field name="th_program_type" widget="radio" options="{'horizontal': true}"/>
                            <field name="th_production_office_id" options="{'no_create': True, 'no_edit': True}"/>
                        </group>
                        <group>
                            <field name="th_responsible_user_id" options="{'no_create': True, 'no_edit': True}"/>
                        </group>
                    </group>

                    <!-- <PERSON><PERSON><PERSON> qu<PERSON> báo cáo -->
                    <group string="Kết quả báo cáo" invisible="context.get('show_report_result', False) != True">
                        <field name="th_report_line_ids" nolabel="1">
                            <tree string="Thống kê khóa học" create="false" edit="false" delete="false">
                                <field name="th_group_name" string="Tên nhóm"/>
                                <field name="th_program_type_display" string="Loại chương trình"/>
                                <field name="th_production_office" string="Phòng sản xuất"/>
                                <field name="th_responsible_user" string="Người phụ trách"/>
                                <field name="th_course_count" string="Số lượng khóa học" sum="Tổng cộng"/>
                                <button name="th_action_view_courses" type="object" 
                                        string="Xem chi tiết" class="btn-link" 
                                        icon="fa-external-link"/>
                            </tree>
                        </field>
                    </group>
                </sheet>
                
                <footer>
                    <!-- Nút cho màn hình lọc -->
                    <button name="th_generate_report" type="object" 
                            string="Tạo báo cáo" class="btn-primary"
                            invisible="context.get('show_report_result', False) == True"/>
                    
                    <!-- Nút cho màn hình kết quả -->
                    <button name="th_export_excel" type="object" 
                            string="Xuất Excel" class="btn-primary"
                            invisible="context.get('show_report_result', False) != True"/>
                    
                    <button string="Đóng" class="btn-secondary" special="cancel"/>
                </footer>
            </form>
        </field>
    </record>

    <!-- Action để mở wizard -->
    <record id="th_course_report_wizard_action" model="ir.actions.act_window">
        <field name="name">Báo cáo thống kê khóa học</field>
        <field name="type">ir.actions.act_window</field>
        <field name="res_model">th.course.report.wizard</field>
        <field name="view_mode">form</field>
        <field name="target">new</field>
        <field name="view_id" ref="th_course_report_wizard_form"/>
    </record>

    <!-- Report Line Tree View (cho trường hợp cần xem riêng) -->
    <record id="th_course_report_line_tree" model="ir.ui.view">
        <field name="name">th.course.report.line.tree</field>
        <field name="model">th.course.report.line</field>
        <field name="arch" type="xml">
            <tree string="Kết quả báo cáo" create="false" edit="false" delete="false">
                <header>
                    <button name="th_export_excel_from_pivot" type="object"
                            string="Xuất Excel" class="btn-primary"
                            icon="fa-download"/>
                </header>
                <field name="th_group_name" string="Tên nhóm"/>
                <field name="th_program_type_display" string="Loại chương trình"/>
                <field name="th_production_office" string="Phòng sản xuất"/>
                <field name="th_responsible_user" string="Người phụ trách"/>
                <field name="th_course_count" string="Số lượng khóa học" sum="Tổng cộng"/>
                <button name="th_action_view_courses" type="object"
                        string="Xem chi tiết" class="btn-link"
                        icon="fa-external-link"/>
            </tree>
        </field>
    </record>

    <!-- Report Line Form View -->
    <record id="th_course_report_line_form" model="ir.ui.view">
        <field name="name">th.course.report.line.form</field>
        <field name="model">th.course.report.line</field>
        <field name="arch" type="xml">
            <form string="Chi tiết nhóm báo cáo">
                <sheet>
                    <div class="oe_title">
                        <h1>
                            <field name="th_group_name" readonly="1"/>
                        </h1>
                    </div>
                    
                    <group>
                        <group>
                            <field name="th_program_type_display" readonly="1"/>
                            <field name="th_production_office" readonly="1"/>
                        </group>
                        <group>
                            <field name="th_responsible_user" readonly="1"/>
                            <field name="th_course_count" readonly="1"/>
                        </group>
                    </group>

                    <group string="Danh sách khóa học">
                        <field name="th_course_ids" nolabel="1">
                            <tree string="Khóa học" create="false" edit="false">
                                <field name="th_course_code" string="Mã khóa học"/>
                                <field name="th_project_template_id" string="Tên khóa học"/>
                                <field name="th_level_production_id" string="Level"/>
                                <field name="th_user_id" string="Người phụ trách"/>
                                <field name="th_production_status" string="Tình trạng sản xuất"/>
                            </tree>
                        </field>
                    </group>
                </sheet>
            </form>
        </field>
    </record>

    <!-- Pivot View cho Report Line -->
    <record id="th_course_report_line_pivot" model="ir.ui.view">
        <field name="name">th.course.report.line.pivot</field>
        <field name="model">th.course.report.line</field>
        <field name="arch" type="xml">
            <pivot string="Phân tích thống kê khóa học">
                <!-- <header>
                    <button name="th_export_excel_from_pivot" type="object"
                            string="Xuất Excel" class="btn-primary"
                            icon="fa-download"/>
                </header> -->
                <!-- Cấu hình mặc định - sẽ được override bởi search filters -->
                <field name="th_group_name" type="row"/>
                <field name="th_course_count" type="measure"/>
                <!-- Các trường có thể dùng để group by -->
                <field name="th_program_type" invisible="1"/>
                <field name="th_production_office_id" invisible="1"/>
                <field name="th_responsible_user_id" invisible="1"/>
            </pivot>
        </field>
    </record>

    <!-- Graph View cho Report Line -->
    <record id="th_course_report_line_graph" model="ir.ui.view">
        <field name="name">th.course.report.line.graph</field>
        <field name="model">th.course.report.line</field>
        <field name="arch" type="xml">
            <graph string="Biểu đồ thống kê khóa học" type="bar">
                <field name="th_group_name"/>
                <field name="th_course_count" type="measure"/>
            </graph>
        </field>
    </record>

    <!-- Search View cho Report Line -->
    <record id="th_course_report_line_search" model="ir.ui.view">
        <field name="name">th.course.report.line.search</field>
        <field name="model">th.course.report.line</field>
        <field name="arch" type="xml">
            <search string="Tìm kiếm báo cáo">
                <field name="th_group_name" string="Tên nhóm"/>
                <field name="th_program_type_display" string="Loại chương trình"/>
                <field name="th_production_office" string="Phòng sản xuất"/>
                <field name="th_responsible_user" string="Người phụ trách"/>

                <separator/>
                <filter string="Ngắn hạn" name="filter_short_term"
                        domain="[('th_program_type', '=', 'short')]"/>
                <filter string="Dài hạn" name="filter_long_term"
                        domain="[('th_program_type', '=', 'long')]"/>
                <filter string="Hỗn hợp" name="filter_mixed"
                        domain="[('th_program_type', '=', 'mixed')]"/>

                <separator/>
                <group expand="0" string="Nhóm theo">
                    <filter string="Loại chương trình" name="group_by_program_type"
                            context="{'group_by': 'th_program_type'}"/>
                    <filter string="Phòng sản xuất" name="group_by_production_office"
                            context="{'group_by': 'th_production_office_id'}"/>
                    <filter string="Người phụ trách" name="group_by_responsible_user"
                            context="{'group_by': 'th_responsible_user_id'}"/>
                </group>
            </search>
        </field>
    </record>

    <!-- Action cho Report Line -->
    <record id="th_course_report_line_action" model="ir.actions.act_window">
        <field name="name">Kết quả báo cáo thống kê</field>
        <field name="type">ir.actions.act_window</field>
        <field name="res_model">th.course.report.line</field>
        <field name="view_mode">tree,form</field>
        <field name="view_id" ref="th_course_report_line_tree"/>
        <field name="search_view_id" ref="th_course_report_line_search"/>
    </record>

    <!-- Pivot View cho th.course.lpm2 -->
    <record id="th_course_lpm2_pivot_report" model="ir.ui.view">
        <field name="name">th.course.lpm2.pivot.report</field>
        <field name="model">th.course.lpm2</field>
        <field name="arch" type="xml">
            <pivot string="Phân tích thống kê khóa học">
                <!-- Cấu hình mặc định -->
                <field name="th_project_id" type="row"/>
                <field name="id" type="measure"/>
                <!-- Các trường có thể dùng để group by -->
                <field name="th_type_course" invisible="1"/>
                <field name="th_project_id" invisible="1"/>
                <field name="th_user_id" invisible="1"/>
                <field name="th_level_production_id" invisible="1"/>
                <field name="th_origin_id" invisible="1"/>
                <field name="th_production_status" invisible="1"/>
            </pivot>
        </field>
    </record>

    <!-- Graph View cho th.course.lpm2 -->
    <record id="th_course_lmp2_graph_report" model="ir.ui.view">
        <field name="name">th.course.lpm2.graph.report</field>
        <field name="model">th.course.lpm2</field>
        <field name="arch" type="xml">
            <graph string="Biểu đồ thống kê khóa học" type="bar">
                <field name="th_project_id"/>
                <field name="id" type="measure"/>
            </graph>
        </field>
    </record>

    <!-- Search View cho th.course.lpm2 (báo cáo) -->
    <record id="th_course_lpm2_search_report" model="ir.ui.view">
        <field name="name">th.course.lpm2.search.report</field>
        <field name="model">th.course.lpm2</field>
        <field name="arch" type="xml">
            <search string="Tìm kiếm khóa học">
                <field name="th_course_code" string="Mã khóa học"/>
                <field name="th_project_template_id" string="Tên khóa học"/>
                <field name="th_user_id" string="Người phụ trách"/>
                <field name="th_project_id" string="Dự án"/>

                <separator/>
                <filter string="Ngắn hạn" name="filter_short_term"
                        domain="[('th_type_course', '=', 'short')]"/>
                <filter string="Dài hạn" name="filter_long_term"
                        domain="[('th_type_course', '=', 'long')]"/>

                <separator/>
                <filter string="Chưa sản xuất" name="filter_not_started"
                        domain="[('th_production_status', '=', 'not_started')]"/>
                <filter string="Đang sản xuất" name="filter_in_progress"
                        domain="[('th_production_status', '=', 'in_progress')]"/>
                <filter string="Đã sản xuất" name="filter_done"
                        domain="[('th_production_status', '=', 'done')]"/>

                <separator/>
                <group expand="0" string="Nhóm theo">
                    <filter string="Loại chương trình" name="group_by_program_type"
                            context="{'group_by': 'th_type_course'}"/>
                    <!-- <filter string="Phòng sản xuất" name="group_by_production_office"
                            context="{'group_by': 'th_project_id.th_production_office_id'}"/> -->
                    <filter string="Người phụ trách" name="group_by_responsible_user"
                            context="{'group_by': 'th_user_id'}"/>
                    <filter string="Dự án" name="group_by_project"
                            context="{'group_by': 'th_project_id'}"/>
                    <filter string="Level sản xuất" name="group_by_level"
                            context="{'group_by': 'th_level_production_id'}"/>
                    <filter string="Đơn vị sử dụng" name="group_by_origin"
                            context="{'group_by': 'th_origin_id'}"/>
                    <filter string="Tình trạng sản xuất" name="group_by_production_status"
                            context="{'group_by': 'th_production_status'}"/>
                </group>
            </search>
        </field>
    </record>

    <!-- Action cho Pivot/Graph Analysis -->
    <record id="th_course_report_analysis_action" model="ir.actions.act_window">
        <field name="name">Phân tích thống kê khóa học</field>
        <field name="type">ir.actions.act_window</field>
        <field name="res_model">th.course.report.line</field>
        <field name="view_mode">pivot,graph</field>
        <field name="view_ids" eval="[(5, 0, 0),
            (0, 0, {'view_mode': 'pivot', 'view_id': ref('th_course_report_line_pivot')}),
            (0, 0, {'view_mode': 'graph', 'view_id': ref('th_course_report_line_graph')})]"/>
        <field name="search_view_id" ref="th_course_report_line_search"/>
    </record>

</odoo>
