<odoo>
    <!-- Wizard Form View -->
    <record id="th_course_report_wizard_form" model="ir.ui.view">
        <field name="name">th.course.report.wizard.form</field>
        <field name="model">th.course.report.wizard</field>
        <field name="arch" type="xml">
            <form string="Báo cáo thống kê khóa học">
                <sheet>
                    <!-- Tiêu chí lọc -->
                    <group string="Tiêu chí lọc báo cáo" invisible="context.get('show_report_result', False) == True">
                        <group>
                            <field name="th_program_type" widget="radio" options="{'horizontal': true}"/>
                            <field name="th_production_office_id" options="{'no_create': True, 'no_edit': True}"/>
                        </group>
                        <group>
                            <field name="th_responsible_user_id" options="{'no_create': True, 'no_edit': True}"/>
                        </group>
                    </group>
                </sheet>
                
                <footer>
                    <!-- <PERSON><PERSON>t cho màn hình lọc -->
                    <button name="th_generate_report" type="object" 
                            string="Tạo báo cáo" class="btn-primary"
                            invisible="context.get('show_report_result', False) == True"/>
                    
                    <!-- Nút cho màn hình kết quả -->
                    <!-- <button name="th_export_excel" type="object" 
                            string="Xuất Excel" class="btn-primary"
                            invisible="context.get('show_report_result', False) != True"/> -->
                    
                    <button string="Đóng" class="btn-secondary" special="cancel"/>
                </footer>
            </form>
        </field>
    </record>

    <!-- Action để mở wizard -->
    <record id="th_course_report_wizard_action" model="ir.actions.act_window">
        <field name="name">Báo cáo thống kê khóa học</field>
        <field name="type">ir.actions.act_window</field>
        <field name="res_model">th.course.report.wizard</field>
        <field name="view_mode">form</field>
        <field name="target">new</field>
        <field name="view_id" ref="th_course_report_wizard_form"/>
    </record>

    

    <!-- Pivot View cho th.course.lpm2 -->
    <record id="th_course_lpm2_pivot_report" model="ir.ui.view">
        <field name="name">th.course.lpm2.pivot.report</field>
        <field name="model">th.course.lpm2</field>
        <field name="arch" type="xml">
            <pivot string="Phân tích thống kê khóa học">
                <!-- Cấu hình mặc định -->
                <field name="th_type_course" type="row"/>
                <field name="th_production_office" type="row"/>
                <field name="th_user_id" type="row"/>
            </pivot>
        </field>
    </record>

    <!-- Graph View cho th.course.lpm2 -->
    <record id="th_course_lpm2_graph_report" model="ir.ui.view">
        <field name="name">th.course.lpm2.graph.report</field>
        <field name="model">th.course.lpm2</field>
        <field name="arch" type="xml">
            <graph string="Biểu đồ thống kê khóa học" type="bar">
                <field name="th_type_course"/>
            </graph>
        </field>
    </record>

    <!-- Search View cho th.course.lpm2 (báo cáo) -->
    <record id="th_course_lpm2_search_report" model="ir.ui.view">
        <field name="name">th.course.lpm2.search.report</field>
        <field name="model">th.course.lpm2</field>
        <field name="arch" type="xml">
            <search string="Tìm kiếm khóa học">
                <field name="th_course_code" string="Mã khóa học"/>
                <field name="th_project_template_id" string="Tên khóa học"/>
                <field name="th_user_id" string="Người phụ trách"/>
                <field name="th_project_id" string="Dự án"/>

                <separator/>
                <filter string="Ngắn hạn" name="filter_short_term"
                        domain="[('th_type_course', '=', 'short')]"/>
                <filter string="Dài hạn" name="filter_long_term"
                        domain="[('th_type_course', '=', 'long')]"/>

                <separator/>
                <filter string="Chưa sản xuất" name="filter_not_started"
                        domain="[('th_production_status', '=', 'not_started')]"/>
                <filter string="Đang sản xuất" name="filter_in_progress"
                        domain="[('th_production_status', '=', 'in_progress')]"/>
                <filter string="Đã sản xuất" name="filter_done"
                        domain="[('th_production_status', '=', 'done')]"/>

                <separator/>
                <group expand="0" string="Nhóm theo">
                    <filter string="Loại chương trình" name="group_by_program_type"
                            context="{'group_by': 'th_type_course'}"/>
                    <!-- <filter string="Phòng sản xuất" name="group_by_production_office"
                            context="{'group_by': 'th_project_id.th_production_office_id'}"/> -->
                    <filter string="Người phụ trách" name="group_by_responsible_user"
                            context="{'group_by': 'th_user_id'}"/>
                    <filter string="Dự án" name="group_by_project"
                            context="{'group_by': 'th_project_id'}"/>
                    <filter string="Level sản xuất" name="group_by_level"
                            context="{'group_by': 'th_level_production_id'}"/>
                    <filter string="Đơn vị sử dụng" name="group_by_origin"
                            context="{'group_by': 'th_origin_id'}"/>
                    <filter string="Tình trạng sản xuất" name="group_by_production_status"
                            context="{'group_by': 'th_production_status'}"/>
                </group>
            </search>
        </field>
    </record>

</odoo>
